// TomTom路由优化工具
// 使用TomTom API计算最优停靠点顺序

// 添加初始化日志
console.log('[TomTom] tomtomRouting.js 模块正在加载...');

import { API_KEYS, WAREHOUSE, SHIFTS, ROUTE_OPTIMIZATION } from '../config';

// TomTom API Key
const TOMTOM_API_KEY = API_KEYS.TOMTOM;
console.log('[TomTom] API Key 加载状态:', TOMTOM_API_KEY ? '成功' : '失败');

// 仓库地址坐标
const DEFAULT_WAREHOUSE = WAREHOUSE;

/**
 * 计算订单的最优访问顺序
 * @param {Array} orders - 订单数组，每个订单必须包含lng_lat或location属性
 * @param {Object} config - 配置对象
 * @param {Object} config.startPoint - 起点坐标 {latitude, longitude}
 * @param {Object} config.endPoint - 终点坐标 {latitude, longitude}
 * @param {String} config.shift - 班次类型 ('morning', 'afternoon', 'evening')
 * @param {Object} config.driver - 司机对象，包含地址坐标
 * @param {Object} config.warehouse - 仓库坐标 {latitude, longitude}
 * @returns {Promise<Array>} - 返回按最优顺序排列的订单数组
 */
export async function calculateOptimalOrderSequence(orders, config = {}) {
  console.log(`[TomTom] 调用calculateOptimalOrderSequence，共${orders?.length || 0}个订单，配置:`, JSON.stringify(config));
  console.log(`[TomTom] ROUTE_OPTIMIZATION.enabled = ${ROUTE_OPTIMIZATION.enabled}`);

  // 如果路线优化未启用，直接返回原始订单
  if (!ROUTE_OPTIMIZATION.enabled) {
    console.log('TomTom路线优化已禁用，使用原始订单顺序');
    return orders;
  }

  try {
    // 验证订单数组非空且每个订单有坐标
    if (!orders || !Array.isArray(orders) || orders.length === 0) {
      console.warn('无效的订单数组');
      return orders;
    }

    // 打印前5个订单的坐标信息，帮助调试
    console.log('[TomTom] 订单坐标示例(前5个):');
    orders.slice(0, 5).forEach((order, index) => {
      console.log(`订单${index+1}:`, JSON.stringify({
        id: order.id,
        lng_lat: order.lng_lat,
        location: order.location && {
          latitude: order.location.latitude,
          longitude: order.location.longitude
        }
      }));
    });

    // 过滤掉没有坐标的订单
    const ordersWithCoords = orders.filter(order => {
      const hasCoords = (order.lng_lat && Array.isArray(order.lng_lat) && order.lng_lat.length === 2) ||
             (order.location && order.location.latitude && order.location.longitude);
      if (!hasCoords) {
        console.log(`[TomTom] 订单 ${order.id || '未知'} 没有有效坐标`);
      }
      return hasCoords;
    });

    console.log(`[TomTom] 过滤后有坐标的订单: ${ordersWithCoords.length}/${orders.length}`);

    if (ordersWithCoords.length === 0) {
      console.warn('[TomTom] 所有订单都没有有效坐标，无法优化路线');
      return orders;
    }

    // 仓库坐标
    const warehouse = config.warehouse || DEFAULT_WAREHOUSE;

    // 确定起点和终点
    let startPoint, endPoint;
    const shift = config.shift || 'morning';
    const driver = config.driver || {};

    // 根据班次确定起点和终点
    if (shift === 'morning' || shift === SHIFTS.MORNING.value) {
      // 上午班：起点是司机地址，终点是仓库
      startPoint = getDriverLocation(driver);
      endPoint = warehouse;
    } else if (shift === 'afternoon' || shift === SHIFTS.AFTERNOON.value) {
      // 下午班：起点和终点都是仓库
      startPoint = warehouse;
      endPoint = warehouse;
    } else if (shift === 'evening' || shift === SHIFTS.EVENING.value) {
      // 晚班：起点是仓库，终点是司机地址
      startPoint = warehouse;
      endPoint = getDriverLocation(driver);
    } else {
      // 默认：使用配置中指定的点
      startPoint = config.startPoint || warehouse;
      endPoint = config.endPoint || warehouse;
    }

    // 转换为TomTom API所需的坐标格式
    const waypoints = [];

    // 添加起点
    if (startPoint && startPoint.latitude && startPoint.longitude) {
      waypoints.push(`${startPoint.latitude},${startPoint.longitude}`);
    }

    // 添加订单坐标
    ordersWithCoords.forEach(order => {
      let lat, lon;
      if (order.lng_lat && Array.isArray(order.lng_lat)) {
        // 使用lng_lat数组 [lat, lon]
        lat = order.lng_lat[0];
        lon = order.lng_lat[1];
      } else if (order.location) {
        // 使用location对象
        lat = order.location.latitude;
        lon = order.location.longitude;
      }

      if (lat && lon) {
        waypoints.push(`${lat},${lon}`);
      }
    });

    // 添加终点
    if (endPoint && endPoint.latitude && endPoint.longitude) {
      waypoints.push(`${endPoint.latitude},${endPoint.longitude}`);
    }

    // 构造TomTom API URL
    const coords = waypoints.join(':');
    const url = `https://api.tomtom.com/routing/1/calculateRoute/${coords}/json?` +
      `computeBestOrder=true&` +
      `routeType=${ROUTE_OPTIMIZATION.routeType || 'fastest'}&` +
      `traffic=${ROUTE_OPTIMIZATION.considerTraffic ? 'true' : 'false'}&` +
      `computeTravelTimeFor=all&` +
      `vehicleEngineType=${ROUTE_OPTIMIZATION.vehicleEngineType || 'combustion'}&` +
      `avoid=tollRoads&` +
      `key=${TOMTOM_API_KEY}`;

    console.log(`[TomTom] 发送路线优化请求，共 ${waypoints.length} 个点，其中 ${ordersWithCoords.length} 个订单点`);
    console.log(`[TomTom] API Key: ${TOMTOM_API_KEY ? '已设置' : '未设置'}`);

    // 隐藏完整URL但展示路径和参数数量，避免暴露API密钥
    const urlParts = url.split('?');
    const params = urlParts[1].split('&');
    console.log(`[TomTom] 请求URL: ${urlParts[0]}?...，包含${params.length}个参数`);

    try {
        // 调用TomTom API
        console.log('[TomTom] 开始fetch请求...');
        const response = await fetch(url);
        console.log(`[TomTom] 收到响应，状态码: ${response.status}`);

        if (!response.ok) {
            console.error(`[TomTom] API请求失败: ${response.status} ${response.statusText}`);
            console.log('[TomTom] 尝试获取错误详情...');
            const errorText = await response.text();
            console.error('[TomTom] 错误详情:', errorText);
            return orders;
        }

        const data = await response.json();
        console.log('[TomTom] 成功解析响应数据');

        // 检查API响应
        if (!data || !data.optimizedWaypoints) {
            console.error('TomTom API响应无效:', data);
            return orders;
        }

        console.log(`[TomTom] 成功获取优化路线，optimizedWaypoints长度: ${data.optimizedWaypoints.length}`);
    } catch (apiError) {
        console.error('[TomTom] API调用出错:', apiError);
        return orders;
    }

    // 获取优化顺序
    const mapping = data.optimizedWaypoints;

    // 获取优化后的索引序列（跳过起点和终点）
    const sortedIndices = mapping
      .filter(wp => wp.providedIndex > 0 && wp.providedIndex < waypoints.length - 1)
      .sort((a, b) => a.optimizedIndex - b.optimizedIndex)
      .map(wp => wp.providedIndex - 1); // 减1是因为waypoints包含起点，索引需要调整

    // 根据优化顺序重排订单
    const optimizedOrders = sortedIndices.map(idx => ordersWithCoords[idx]);

    // 将无坐标的订单添加到结果末尾（保持原始顺序）
    const ordersWithoutCoords = orders.filter(order => {
      return !((order.lng_lat && Array.isArray(order.lng_lat) && order.lng_lat.length === 2) ||
               (order.location && order.location.latitude && order.location.longitude));
    });

    // 合并结果
    const result = [...optimizedOrders, ...ordersWithoutCoords];

    // 记录日志
    console.log(`[TomTom] 优化路线结果: ${result.length}个订单已按最优顺序排列`);

    return result;
  } catch (error) {
    console.error('TomTom路线优化失败:', error);
    // 发生错误时返回原始订单顺序
    return orders;
  }
}

/**
 * 从司机对象中提取位置信息
 * @param {Object} driver - 司机对象
 * @returns {Object} - 返回包含latitude和longitude的对象
 */
function getDriverLocation(driver) {
  // 如果司机对象中有明确的位置信息，使用它
  if (driver.location && driver.location.latitude && driver.location.longitude) {
    return {
      latitude: driver.location.latitude,
      longitude: driver.location.longitude
    };
  }

  // 或者通过其他方式确定司机位置
  if (driver.latitude && driver.longitude) {
    return {
      latitude: driver.latitude,
      longitude: driver.longitude
    };
  }

  // 如果没有直接的坐标，但有地址信息，可以考虑添加地理编码逻辑
  // ...

  // 无法确定司机位置，返回默认位置
  return DEFAULT_WAREHOUSE;
}

/**
 * 更新订单的停靠点编号，根据优化后的顺序
 * @param {Array} optimizedOrders - 优化后顺序的订单数组
 * @returns {Array} - 返回更新了stop_no的订单数组
 */
export function updateOrderStopNumbers(optimizedOrders) {
  if (!optimizedOrders || !Array.isArray(optimizedOrders)) {
    return [];
  }

  // 为每个订单分配新的停靠点编号
  return optimizedOrders.map((order, index) => {
    // 创建新对象，避免修改原始对象
    return {
      ...order,
      stop_no: index + 1
    };
  });
}