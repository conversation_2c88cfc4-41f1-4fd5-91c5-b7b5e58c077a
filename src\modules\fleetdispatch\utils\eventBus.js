/**
 * 事件总线模块 - 实现基于发布-订阅模式的组件间通信
 * 用于降低组件间耦合度，统一事件处理机制
 */

import mitt from 'mitt'
import { throttle } from 'lodash-es'

// 创建基础事件总线
const emitter = mitt()

/**
 * 事件类型常量 - 确保事件名称一致性，避免拼写错误
 */
export const EVENT_TYPES = {
    // 订单相关事件
    ORDER_SELECTED: 'order:selected',
    ORDER_DESELECTED: 'order:deselected',
    ORDERS_BATCH_SELECTED: 'orders:batch-selected',
    ORDERS_SELECTION_CLEARED: 'orders:selection-cleared',
    ORDERS_FILTERED: 'orders:filtered',
    ORDERS_UPDATED: 'orders:updated', // 全量或批量更新
    ORDERS_UNASSIGNED: 'orders:unassigned', // 专门用于取消分配订单的事件
    ORDER_LIST_UPDATED: 'orders:list-updated', // 用于通知订单列表需要更新的事件
    ORDER_COORDINATES_UPDATED: 'order:coordinates-updated', // 用于通知单个订单坐标已更新
    ORDER_COORDINATES_BATCH_UPDATED: 'order:coordinates-batch-updated', // 新增: 用于通知多个订单坐标已批量更新
    ORDER_UPDATED: 'orders:order-updated',     // 新增: 单个订单更新
    ORDER_ADDED: 'orders:order-added',         // 新增: 单个订单添加
    ORDER_REMOVED: 'orders:order-removed',     // 新增: 单个订单移除 (可选)

    // 地图相关事件
    MAP_ORDER_CLICKED: 'map:order-clicked',
    MAP_UPDATING: 'map:updating',
    MAP_READY: 'map:ready',
    MAP_REFRESH: 'map:refresh', // 新增：强制刷新地图
    DRAG_START: 'map:drag-start', // 新增：拖拽开始事件

    // 司机相关事件
    DRIVER_SELECTED: 'driver:selected',
    DRIVER_SELECTION_CHANGED: 'driver:selection-changed',

    // 路线相关事件
    ROUTE_SELECTED: 'route:selected',
    ROUTE_UPDATED: 'route:updated',
    DISPLAY_MODE_CHANGED: 'display:mode-changed',
    DRIVER_UPDATED: 'driver:updated',

    // 数据切换相关事件
    DATA_SWITCHING: 'data:switching',

    // Firebase消息相关事件
    FIREBASE_MESSAGE_RECEIVED: 'firebase:message-received',
    FIREBASE_NEW_ORDER: 'firebase:new-order',
    FIREBASE_DRIVER_LOCATION_UPDATE: 'firebase:driver-location-update',
    FIREBASE_ORDER_STATUS_CHANGE: 'firebase:order-status-change',
    FIREBASE_TOKEN_UPDATED: 'firebase:token-updated',

    // 调度系统通知相关事件
    DISPATCHER_NOTIFICATION_RECEIVED: 'dispatcher:notification-received',
    DRIVER_POSITIONS_UPDATED: 'driver:positions-updated'
}

// 记录事件频率
const eventStats = {
    counts: {},
    lastEmitted: {},
    throttled: {}
}

// 创建优化的事件总线
export const eventBus = {
    // 节流版本的emit函数
    emit: (type, data) => {
    // 记录事件次数
        eventStats.counts[type] = (eventStats.counts[type] || 0) + 1

        // 检查事件是否需要节流
        const now = Date.now()
        const lastTime = eventStats.lastEmitted[type] || 0
        const throttleTime = getThrottleTimeForEvent(type, data) // 传递data参数

        // 如果事件需要节流且时间间隔太短，则跳过
        if (throttleTime > 0 && now - lastTime < throttleTime) {
            // 对于拖拽操作，即使在节流时间内也允许更新
            if (type === EVENT_TYPES.ORDERS_UPDATED && data && data.isDragOperation) {
                // 拖拽操作不节流，继续执行
            } else {
                return
            }
        }

        // 更新最后发送时间
        eventStats.lastEmitted[type] = now

        // 实际发送事件
        emitter.emit(type, data)

        // 高频事件警告
        if (eventStats.counts[type] > 50) {
            console.warn(`高频事件警告: ${type} 已触发 ${eventStats.counts[type]} 次`)

            // 每100次重置计数
            if (eventStats.counts[type] % 100 === 0) {
                eventStats.counts[type] = 0
            }
        }
    },

    // 普通监听函数
    on: (type, handler) => {
        emitter.on(type, handler)
    },

    // 移除监听函数
    off: (type, handler) => {
        emitter.off(type, handler)
    },

    // 节流版本的监听函数
    onThrottled: (type, handler, wait = 300) => {
        const throttledHandler = throttle(handler, wait)
        emitter.on(type, throttledHandler)

        // 返回原始处理函数，以便后续可以移除
        return throttledHandler
    },

    // 返回事件统计信息
    getStats: () => {
        return { ...eventStats.counts }
    },

    // 重置事件统计
    resetStats: () => {
        for (const key in eventStats.counts) {
            eventStats.counts[key] = 0
        }
    }
}

// 不同事件类型的推荐节流时间
function getThrottleTimeForEvent(type, data) {
    // 检查是否是拖拽操作导致的更新，如果是则不节流
    if (type === EVENT_TYPES.ORDERS_UPDATED && data && data.isDragOperation) {
        return 0; // 拖拽操作不节流，确保实时更新
    }

    switch (type) {
        case EVENT_TYPES.ORDERS_FILTERED:
            return 500 // 订单过滤事件节流500ms
        case EVENT_TYPES.ORDER_SELECTED:
            return 300 // 订单选择事件节流300ms
        case EVENT_TYPES.MAP_UPDATING:
            return 1000 // 地图更新事件节流1000ms
        case EVENT_TYPES.DISPLAY_MODE_CHANGED:
            return 500 // 显示模式变更节流500ms
        case EVENT_TYPES.ORDER_COORDINATES_UPDATED:
            return 1000 // 订单坐标更新事件节流1000ms，减少高频更新
        case EVENT_TYPES.ORDER_COORDINATES_BATCH_UPDATED:
            return 500 // 批量坐标更新事件节流500ms
        case EVENT_TYPES.ORDERS_UPDATED:
            return 100 // 订单更新事件节流时间减少到100ms，提高响应速度
        default:
            return 0 // 其他事件不节流
    }
}

// 导出一个工具函数，用于在Vue组件中方便地使用eventBus
export function useEventBus() {
    return {
        eventBus,
        EVENT_TYPES
    }
}

// 添加调试信息，用于追踪事件流转
const originalEmit = eventBus.emit;
eventBus.emit = function(event, ...args) {
    console.log(`[EventBus] 发出事件: ${event}`, args);

    // 如果是司机位置更新事件，打印更多调试信息
    if (event === EVENT_TYPES.DRIVER_POSITIONS_UPDATED) {
        console.log(`[EventBus] 司机位置更新事件触发，数据条数: ${args[0]?.length || 0}`);
        console.trace('[EventBus] 事件触发堆栈');
    }

    return originalEmit.call(this, event, ...args);
};

const originalOn = eventBus.on;
eventBus.on = function(event, handler) {
    console.log(`[EventBus] 注册事件监听: ${event}`);

    // 包装处理函数，添加错误处理
    const wrappedHandler = function(...args) {
        try {
            console.log(`[EventBus] 执行事件处理: ${event}`);
            return handler(...args);
        } catch (error) {
            // 提供更详细的错误信息
            console.error(`[EventBus] 事件处理出错: ${event}`, error);
            console.error(`[EventBus] 错误详情:`, {
                eventType: event,
                errorName: error.name,
                errorMessage: error.message,
                errorStack: error.stack,
                args: args.map(arg => typeof arg === 'object' ?
                               `类型: ${Array.isArray(arg) ? 'Array' : 'Object'}, 长度/键数: ${Array.isArray(arg) ? arg.length : Object.keys(arg).length}` :
                               `类型: ${typeof arg}, 值: ${arg}`)
            });
            // 即使事件处理出错，也不抛出异常，避免中断事件流
        }
    };

    return originalOn.call(this, event, wrappedHandler);
};