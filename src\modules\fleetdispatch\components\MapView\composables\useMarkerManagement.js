import { ref, computed, markRaw, h, createApp } from 'vue';
import { useOrderStore } from '../../../stores/order';
import { useDriverStore } from '../../../stores/driver';
import { useRouteStore } from '../../../stores/route';
import maplibregl from 'maplibre-gl';
import Marker from '../Marker.vue';
import MarkerPopup from '../MarkerPopup.vue';
import { eventBus, EVENT_TYPES } from '../../../utils/eventBus';

export function useMarkerManagement(map, isDragging) {
    const orderStore = useOrderStore();
    const driverStore = useDriverStore();

    // 如果没有传入拖拽状态，默认为 false
    const isCurrentlyDragging = isDragging || ref(false);

    // 标记相关
    const markers = ref(new Map());
    const isUpdatingMarkers = ref(false);
    const popupCache = ref({});
    const maxCachedPopups = 20;
    const performanceStats = ref({ markerCount: 0 });
    const lastUpdateTime = ref(0); // 添加上次更新时间引用

    // 弹窗控制
    const activePopup = ref(null);
    const popupHoverTimer = ref(null);
    const POPUP_HOVER_DELAY = 600;
    let currentlyHoveredOrderId = null; // 跟踪当前悬停的订单ID
    const lastPopupTime = ref(0);
    const popupDebounceTime = 500;
    const lastClickTime = ref(0);
    const clickDebounceTime = 300;

    // 记录当前选中的订单
    const selectedOrders = ref(new Set());

    // 创建的图标集合，用于跟踪和删除
    const createdIcons = ref(new Set());

    // 创建自定义标记元素
    const createCustomMarkerElement = order => {
        // 使用Vue组件创建DOM
        const app = document.createElement('div');

        // 检查订单是否已分配
        const isAssigned = !!order.driver_id;

        // 将stop_no转换为字符串类型，防止类型错误
        const stopText = isAssigned ? (order.stop_no ? String(order.stop_no) : '') : '';

        // 检查是否为选中的订单 - 使用orderStore中的数据确保最新状态
        const isSelected = orderStore.selectedOrderIds.has(order.id);

        // 打印调试信息
        console.log('创建标记元素状态:', {
            id: order.id,
            isSelected: isSelected,
            originalSelected: order.isSelected,
            inStore: orderStore.selectedOrderIds.has(order.id)
        });

        const orderData = {
            id: order.id,
            text: stopText, // 确保text始终是字符串
            markerColor: getMarkerColor(order),
            isAssigned: isAssigned,
            isSelected: isSelected // 使用从orderStore获取的选中状态
        };

        // 保存订单数据到DOM元素，便于后续更新
        app._orderData = orderData;

        // 挂载自定义Marker组件并监听点击事件
        const instance = createApp({
            render() {
                return h(Marker, {
                    ...orderData,
                    onClickMarker: data => {
                        console.log('Marker点击事件 - 检测Ctrl键:', data.id);

                        // 使用防抖，避免重复点击
                        const now = Date.now();
                        if (now - lastClickTime.value < clickDebounceTime) {
                            console.log('点击过于频繁，忽略此次点击');
                            return;
                        }
                        lastClickTime.value = now;

                        // 检查是否按下了Ctrl键（多选模式）
                        // 注意：通过window.event获取当前点击事件
                        const isMultiSelect = window.event && (window.event.ctrlKey || window.event.metaKey);
                        console.log('是否按下Ctrl键:', isMultiSelect);

                        if (isMultiSelect) {
                            // Ctrl键多选模式：切换当前订单选择状态
                            if (orderStore.selectedOrderIds.has(data.id)) {
                                orderStore.selectedOrderIds.delete(data.id);
                            } else {
                                orderStore.selectedOrderIds.add(data.id);
                            }
                        } else {
                            // 单选模式：先清除所有选择，再选中当前订单
                            // 如果当前订单已经选中，则清除选择
                            const isCurrentlySelected = orderStore.selectedOrderIds.has(data.id);

                            if (isCurrentlySelected) {
                                // 如果已经选中，则清除选择
                                orderStore.selectedOrderIds.clear();
                            } else {
                                // 先清除所有选择，再选中当前订单
                                orderStore.selectedOrderIds.clear();
                                orderStore.selectedOrderIds.add(data.id);
                            }
                        }

                        // 通知其他组件选择状态已变更
                        notifyOrderSelectionChanged();

                        // 强制更新当前marker
                        setTimeout(() => {
                            updateMarkerSelection(data.id, orderStore.selectedOrderIds.has(data.id));
                        }, 50);
                    }
                });
            }
        });

        // 添加鼠标悬停事件处理
        app.addEventListener('mouseenter', () => handleMarkerMouseEnter(order));
        app.addEventListener('mouseleave', handleMarkerMouseLeave);

        instance.mount(app);

        // 返回包含Vue组件的DOM元素
        return app;
    };

    // 处理标记鼠标悬停事件
    const handleMarkerMouseEnter = order => {
        // 如果正在拖拽，不处理悬停事件
        if (isCurrentlyDragging.value) {
            console.log('正在拖拽，不处理悬停事件');
            return;
        }

        // 清除任何之前的定时器
        if (popupHoverTimer.value) {
            clearTimeout(popupHoverTimer.value);
        }

        // 设置新的定时器，在POPUP_HOVER_DELAY毫秒后显示弹窗
        popupHoverTimer.value = setTimeout(() => {
            showOrderPopup(order);
        }, POPUP_HOVER_DELAY);
    };

    // 处理标记鼠标离开事件
    const handleMarkerMouseLeave = () => {
        // 清除定时器
        if (popupHoverTimer.value) {
            clearTimeout(popupHoverTimer.value);
            popupHoverTimer.value = null;
        }

        // 设置一个短延时，允许鼠标移动到弹窗上
        setTimeout(() => {
            // 检查鼠标是否已移动到弹窗上
            if (activePopup.value) {
                const popupElement = document.querySelector('.maplibregl-popup');
                if (popupElement && !popupElement.matches(':hover')) {
                    hideOrderPopup();
                }
            }
        }, 100);
    };

    // 获取标记颜色
    const getMarkerColor = order => {
        // 如果订单已分配给司机，显示司机的颜色
        if (order.driver_id) {
            // 从driverStore获取司机颜色
            const driver = driverStore.getDriverById(order.driver_id);
            return driver?.color || '#4CAF50'; // 如果找不到司机，使用默认绿色
        }

        // 未分配的订单显示灰色
        return '#9E9E9E';
    };

    // 通过ID获取订单对象
    const getOrderById = (orderId) => {
        // 优先从当前显示的订单中查找
        // const current = computed(() => { /* ... 获取当前显示订单的逻辑 ... */ });
        // let order = current.value?.find(o => o.id === orderId);
        // if (order) return order;

        // 如果当前显示中没有，则从所有订单中查找
        return orderStore.allOrders.find(order => order.id === orderId);
    };

    // 计算弹窗最佳锚点位置
    const calculateBestAnchor = (lngLat) => {
        if (!map.value) return 'top'; // 默认在标记下方

        // 获取地图容器尺寸
        const mapContainer = map.value.getContainer();
        const mapHeight = mapContainer.offsetHeight;

        // 获取点在地图上的像素坐标
        const pointPixel = map.value.project(lngLat);

        // 估算弹窗高度（通常在150px左右）
        const estimatedPopupHeight = 180;

        // 计算上下方可用空间
        const spaceAbove = pointPixel.y;
        const spaceBelow = mapHeight - pointPixel.y;

        // 首先确定垂直方向位置
        let anchor;
        if (spaceBelow >= estimatedPopupHeight) {
            // 如果下方空间足够，优先放在下方（使用top锚点）
            anchor = 'top';
        } else if (spaceAbove >= estimatedPopupHeight) {
            // 如果上方空间足够，放在上方（使用bottom锚点）
            anchor = 'bottom';
        } else {
            // 两边都不够，选择空间较大的方向
            anchor = spaceBelow > spaceAbove ? 'top' : 'bottom';
        }

        return anchor;
    };

    // 根据不同锚点返回不同的偏移量
    const getPopupOffset = (anchor) => {
        switch (anchor) {
            case 'bottom': // 弹窗在上方
                return [0, -30]; // 使用负值向上移动，避免遮挡标记 (原为 [0, 30])
            case 'top': // 弹窗在下方
            default:
                return [0, -10]; // 保持不变，或根据需要调整
        }
    };

    // 确保弹窗在可视范围内
    const ensurePopupInView = () => {
        if (!activePopup.value || !map.value) return;

        const popupElement = activePopup.value.getElement();
        if (!popupElement) return;

        // 获取地图容器
        const mapContainer = map.value.getContainer();
        const mapRect = mapContainer.getBoundingClientRect();

        // 获取弹窗位置
        const popupRect = popupElement.getBoundingClientRect();

        // 计算弹窗是否超出视图
        const isOutOfTop = popupRect.top < mapRect.top;
        const isOutOfBottom = popupRect.bottom > mapRect.bottom;
        const isOutOfLeft = popupRect.left < mapRect.left;
        const isOutOfRight = popupRect.right > mapRect.right;

        if (isOutOfTop || isOutOfBottom || isOutOfLeft || isOutOfRight) {
            console.log('弹窗超出可视范围，尝试调整位置');

            // 获取弹窗绑定的坐标
            const lngLat = activePopup.value.getLngLat();

            // 保存原始DOM内容
            const originalContentElement = popupElement.querySelector('.marker-popup-container');
            if (!originalContentElement) return; // 如果找不到内容元素，则无法重新创建

            // 移除现有弹窗
            activePopup.value.remove();
            activePopup.value = null; // 重置引用

            // 重新创建弹窗，使用不同的锚点
            let anchor;

            // 优先处理垂直方向
            if (isOutOfTop) {
                anchor = 'top'; // 显示在下方
            } else if (isOutOfBottom) {
                anchor = 'bottom'; // 显示在上方
            } else {
                // 保持当前垂直锚点
                anchor = calculateBestAnchor([lngLat.lng, lngLat.lat]); // 重新计算一下以防万一
            }

            // 处理水平方向
            if (isOutOfLeft) {
                anchor = anchor + '-left'; // 向右偏移
            } else if (isOutOfRight) {
                anchor = anchor + '-right'; // 向左偏移
            }

            console.log('调整后的锚点:', anchor);

            // 创建新的内容元素
            const newContentElement = document.createElement('div');
            newContentElement.className = 'marker-popup-container';
            // 把旧内容移过来，而不是用innerHTML，避免重新渲染Vue组件
            while (originalContentElement.firstChild) {
                 newContentElement.appendChild(originalContentElement.firstChild);
            }


            // 重新创建弹窗
            activePopup.value = new maplibregl.Popup({
                closeButton: false,
                closeOnClick: false,
                maxWidth: '250px', // 从旧代码复制maxWidth
                offset: getPopupOffset(anchor),
                className: 'marker-hover-popup',
                anchor: anchor // 使用新计算的锚点
            })
                .setLngLat(lngLat)
                .setDOMContent(newContentElement) // 使用包含旧Vue组件实例的元素
                .addTo(map.value);

            // 再次添加事件监听器
            const newPopupElement = activePopup.value.getElement();
            if (newPopupElement) {
                newPopupElement.addEventListener('mouseenter', () => {
                    if (popupHoverTimer.value) {
                        clearTimeout(popupHoverTimer.value);
                        popupHoverTimer.value = null;
                    }
                });
                newPopupElement.addEventListener('mouseleave', () => {
                    hideOrderPopup();
                });
            }

            // 如果弹窗依然超出视图，可以尝试平移地图 (这段逻辑可以根据需要保留或移除)
            setTimeout(() => {
                const newPopupElement = activePopup.value?.getElement();
                if (!newPopupElement) return;
                const newPopupRect = newPopupElement.getBoundingClientRect();
                const newMapRect = map.value.getContainer().getBoundingClientRect(); // 重新获取地图边界

                const newIsOutOfView =
                    newPopupRect.top < newMapRect.top ||
                    newPopupRect.bottom > newMapRect.bottom ||
                    newPopupRect.left < newMapRect.left ||
                    newPopupRect.right > newMapRect.right;

                if (newIsOutOfView) {
                    let dx = 0;
                    let dy = 0;

                    if (newPopupRect.top < newMapRect.top) dy = newMapRect.top - newPopupRect.top + 10;
                    else if (newPopupRect.bottom > newMapRect.bottom) dy = newMapRect.bottom - newPopupRect.bottom - 10;

                    if (newPopupRect.left < newMapRect.left) dx = newMapRect.left - newPopupRect.left + 10;
                    else if (newPopupRect.right > newMapRect.right) dx = newMapRect.right - newPopupRect.right - 10;

                    if (dx !== 0 || dy !== 0) {
                        console.log('弹窗调整后仍超出，平移地图:', dx, dy);
                        map.value.panBy([dx, dy], { duration: 300 });
                    }
                }
            }, 50);
        }
    };

    // 显示订单弹窗
    const showOrderPopup = order => {
        // 如果已有弹窗，先关闭
        hideOrderPopup();

        // 如果正在拖拽，不显示弹窗
        if (isCurrentlyDragging.value) {
            console.log('正在拖拽，不显示弹窗');
            return;
        }

        // 检查订单数据
        if (!order || !map.value) return;

        // 获取坐标
        const coords = order.lng_lat || order.location;
        if (!coords || !Array.isArray(coords) || coords.length !== 2) return;

        // MapLibre 需要 [lng, lat]
        const lngLat = [coords[1], coords[0]];

        // 获取该点位所有订单
        const orders = findOrdersAtLocation(coords); // 确保 findOrdersAtLocation 使用 [lat, lng] 比较

        if (orders.length === 0) return;

        // 创建弹窗元素
        const popupElement = document.createElement('div');
        popupElement.className = 'marker-popup-container';

        // 按照MarkerPopup期望的格式创建数据
        const popupData = {
            orders: {
                pickup: orders.filter(o => o.type && o.type.toUpperCase() === 'PICKUP'),
                delivery: orders.filter(o => !o.type || o.type.toUpperCase() !== 'PICKUP')
            }
        };

        // 使用Vue组件渲染弹窗内容
        const popupApp = createApp({
            render() {
                return h(MarkerPopup, {
                    point: popupData
                });
            }
        });

        // 挂载组件
        popupApp.mount(popupElement);

        // *** 修改点：动态计算锚点和偏移量 ***
        const anchor = calculateBestAnchor(lngLat);
        const offset = getPopupOffset(anchor);

        // 创建MapLibre弹窗
        activePopup.value = new maplibregl.Popup({
            closeButton: false,
            closeOnClick: false,
            anchor: anchor, // 使用计算出的锚点
            offset: offset, // 使用计算出的偏移量
            maxWidth: '250px', // 保持最大宽度限制
            className: 'marker-hover-popup'
        })
            .setLngLat(lngLat)
            .setDOMContent(popupElement)
            .addTo(map.value);

        // *** 修改点：添加 ensurePopupInView 调用 ***
        setTimeout(() => {
            ensurePopupInView();
        }, 10); // 短暂延迟后检查

        // 给弹窗添加事件监听
        const popupDomElement = activePopup.value.getElement();
        if (popupDomElement) {
            popupDomElement.addEventListener('mouseenter', () => {
                 if (popupHoverTimer.value) {
                     clearTimeout(popupHoverTimer.value);
                     popupHoverTimer.value = null;
                 }
            });
            popupDomElement.addEventListener('mouseleave', () => {
                 hideOrderPopup();
            });
        }

        lastPopupTime.value = Date.now();
    };

    // 隐藏订单弹窗 (Helper - 确保健壮性)
    const hideOrderPopup = () => {
        const popupToRemove = activePopup.value; // 捕获引用
        if (popupToRemove) {
            activePopup.value = null; // 立即重置状态
            if (typeof popupToRemove.remove === 'function') {
                try { popupToRemove.remove(); } catch (error) { console.warn("Error removing popup:", error); }
            }
        }
        // 额外清除计划中的计时器
        if (popupHoverTimer.value) {
            clearTimeout(popupHoverTimer.value);
            popupHoverTimer.value = null;
        }
    };



    // 更新单个订单的标记（高性能）
    const updateSingleOrderMarker = (order) => {
        if (!map.value || !map.value.getSource('orders-source')) {
            console.warn('地图或数据源不可用');
            return;
        }

        console.log(`更新单个订单标记: ${order.id}`);

        // 获取当前的 GeoJSON 数据
        const source = map.value.getSource('orders-source');
        const currentData = source._data || { type: 'FeatureCollection', features: [] };

        // 获取订单坐标
        const coords = order.lng_lat || order.location;
        if (!coords || !Array.isArray(coords) || coords.length !== 2) {
            console.warn(`订单 ${order.id} 没有有效坐标`);
            return;
        }

        const [lat, lng] = coords;

        // 检查订单是否已分配
        const isAssigned = !!order.driver_id;

        // 获取停靠点编号
        let stopNo = '';
        if (isAssigned) {
            if (order.stop_no) {
                stopNo = String(order.stop_no);
            } else if (order.pickup_stop_no) {
                stopNo = String(order.pickup_stop_no);
            } else if (order.no) {
                const orderNo = String(order.no);
                stopNo = orderNo.slice(-1);
            }
        }

        // 获取司机颜色
        const driverColor = isAssigned ? getMarkerColor(order) : '#9E9E9E';

        // 检查是否为选中的订单
        const isSelected = orderStore.selectedOrderIds.has(order.id);

        // 创建新的图标ID
        const uniqIconSuffix = `${order.id}-${stopNo || '0'}`;
        let iconId;

        if (isSelected) {
            iconId = isAssigned
                ? `selected-assigned-${uniqIconSuffix}`
                : `selected-unassigned-${uniqIconSuffix}`;
        } else {
            iconId = isAssigned
                ? `assigned-${uniqIconSuffix}`
                : `unassigned-${uniqIconSuffix}`;
        }

        // 创建新图标
        if (!map.value.hasImage(iconId)) {
            createSVGMarkerImage(
                iconId,
                isAssigned ? driverColor : '#9E9E9E',
                isAssigned,
                isSelected,
                stopNo,
                order.status,
                order.type && order.type.toUpperCase()
            );
            createdIcons.value.add(iconId);
        }

        // 查找并更新现有的 feature
        const features = currentData.features || [];
        let featureIndex = -1;

        // 查找对应的 feature（可能是单个订单或聚合标记的一部分）
        for (let i = 0; i < features.length; i++) {
            const feature = features[i];
            if (feature.properties.isCluster) {
                // 检查聚合标记是否包含这个订单
                try {
                    const clusterOrderIds = JSON.parse(feature.properties.clusterOrderIds || '[]');
                    if (clusterOrderIds.includes(order.id)) {
                        // 这个订单是聚合标记的一部分，需要重新计算整个聚合
                        console.log(`订单 ${order.id} 是聚合标记的一部分，需要重新计算聚合`);
                        // 暂时跳过，让全量更新处理聚合情况
                        return;
                    }
                } catch (e) {
                    console.warn('解析聚合订单ID失败:', e);
                }
            } else if (feature.properties.id === order.id) {
                // 找到对应的单个订单 feature
                featureIndex = i;
                break;
            }
        }

        if (featureIndex !== -1) {
            // 更新现有的 feature
            const feature = features[featureIndex];
            feature.properties.status = isAssigned ? 'assigned' : 'unassigned';
            feature.properties.stopNo = stopNo;
            feature.properties.driverId = order.driver_id || null;
            feature.properties.routeId = order.route_id || null;
            feature.properties.iconImage = iconId;
            feature.properties.color = driverColor;
            feature.properties.zIndexOffset = isSelected ? 1000 : (isAssigned ? 0 : 500);

            console.log(`更新订单 ${order.id} 的 feature:`, {
                status: feature.properties.status,
                stopNo: feature.properties.stopNo,
                driverId: feature.properties.driverId,
                routeId: feature.properties.routeId,
                isAssigned: isAssigned
            });
        } else {
            // 创建新的 feature
            const newFeature = {
                type: 'Feature',
                geometry: {
                    type: 'Point',
                    coordinates: [lng, lat]
                },
                properties: {
                    id: order.id,
                    status: isAssigned ? 'assigned' : 'unassigned',
                    stopNo: stopNo,
                    driverId: order.driver_id || null,
                    routeId: order.route_id || null,
                    selected: isSelected,
                    iconImage: iconId,
                    color: driverColor,
                    zIndexOffset: isSelected ? 1000 : (isAssigned ? 0 : 500),
                    isCluster: false
                }
            };

            features.push(newFeature);
            console.log(`为订单 ${order.id} 创建新的 feature`);
        }

        // 更新数据源
        source.setData({
            type: 'FeatureCollection',
            features: features
        });

        console.log(`订单 ${order.id} 标记更新完成`);
    };

    // 查找指定位置的所有订单
    const findOrdersAtLocation = (location) => {
        if (!location || !Array.isArray(location) || location.length !== 2) return [];

        // 查找具有相同或非常接近坐标的所有订单
        return orderStore.allOrders.filter(order => {
            const orderCoords = order.lng_lat || order.location;
            if (!orderCoords || !Array.isArray(orderCoords) || orderCoords.length !== 2) return false;

            // 检查坐标是否非常接近（允许微小差异）
            const latDiff = Math.abs(orderCoords[0] - location[0]);
            const lngDiff = Math.abs(orderCoords[1] - location[1]);

            // 如果坐标差异小于阈值，认为是同一位置
            return latDiff < 0.0001 && lngDiff < 0.0001;
        });
    };

    // 添加标记图层 - 使用Symbol Layer方法
    const addMarkerLayers = () => {
        if (!map.value) return;

        // 添加标记数据源
        if (!map.value.getSource('orders-source')) {
            map.value.addSource('orders-source', {
                type: 'geojson',
                data: {
                    type: 'FeatureCollection',
                    features: []
                }
            });

            console.log('创建订单数据源成功');
        }

        // 添加标记图层
        // 1. 未分配订单图层
        if (!map.value.getLayer('unassigned-orders-layer')) {
            map.value.addLayer({
                id: 'unassigned-orders-layer',
                type: 'symbol',
                source: 'orders-source',
                filter: ['==', ['get', 'status'], 'unassigned'],
                layout: {
                    'icon-image': ['get', 'iconImage'],
                    'icon-size': 1.0,
                    'icon-allow-overlap': true,
                    'icon-anchor': 'bottom',
                    'icon-ignore-placement': true
                }
            });
        }

        // 2. 已分配订单图层
        if (!map.value.getLayer('assigned-orders-layer')) {
            map.value.addLayer({
                id: 'assigned-orders-layer',
                type: 'symbol',
                source: 'orders-source',
                filter: ['==', ['get', 'status'], 'assigned'],
                layout: {
                    'icon-image': ['get', 'iconImage'],
                    'icon-size': 1.0,
                    'icon-allow-overlap': true,
                    'icon-anchor': 'bottom',
                    'icon-ignore-placement': true
                }
            });
        }

        // 3. 选中订单图层 (叠加在其他图层上)
        if (!map.value.getLayer('selected-orders-layer')) {
            map.value.addLayer({
                id: 'selected-orders-layer',
                type: 'symbol',
                source: 'orders-source',
                filter: ['==', ['get', 'selected'], true],
                layout: {
                    'icon-image': ['get', 'iconImage'],
                    'icon-size': 1.0,
                    'icon-allow-overlap': true,
                    'icon-anchor': 'bottom',
                    'icon-ignore-placement': true
                }
            });
        }

        console.log('创建标记图层成功');

        // 添加图层事件监听
        if (map.value) {
            // 先移除所有现有的事件监听，防止重复绑定
            const layers = ['unassigned-orders-layer', 'assigned-orders-layer', 'selected-orders-layer'];

            layers.forEach(layer => {
                map.value.off('click', layer, handleSymbolClick);
                map.value.off('mouseenter', layer, handleSymbolMouseEnter);
                map.value.off('mouseleave', layer, handleSymbolMouseLeave);
            });

            // 添加鼠标点击事件处理
            map.value.on('click', 'unassigned-orders-layer', handleSymbolClick);
            map.value.on('click', 'assigned-orders-layer', handleSymbolClick);
            map.value.on('click', 'selected-orders-layer', handleSymbolClick);

            // 添加鼠标悬停事件处理
            map.value.on('mouseenter', 'unassigned-orders-layer', handleSymbolMouseEnter);
            map.value.on('mouseenter', 'assigned-orders-layer', handleSymbolMouseEnter);
            map.value.on('mouseenter', 'selected-orders-layer', handleSymbolMouseEnter);

            // 添加鼠标离开事件处理
            map.value.on('mouseleave', 'unassigned-orders-layer', handleSymbolMouseLeave);
            map.value.on('mouseleave', 'assigned-orders-layer', handleSymbolMouseLeave);
            map.value.on('mouseleave', 'selected-orders-layer', handleSymbolMouseLeave);

            console.log('添加标记图层事件监听成功');
        }
    };

    // 创建与Marker.vue组件样式一致的SVG标记图标
    const createSVGMarkerImage = (id, color, isAssigned, isSelected, stopNumber, orderStatus, orderType) => {
        if (!map.value) return;

        // 检查图标是否已存在
        if (map.value.hasImage(id)) return;

        // 创建一个Canvas元素来绘制SVG
        const canvas = document.createElement('canvas');

        // 设置正确的Canvas尺寸 - 恢复原始大小
        const markerWidth = 32; // 恢复标记宽度 (原为 32)
        const markerHeight = 48; // 恢复标记高度 (原为 48)
        const padding = isSelected ? 16 : 0; // 选中状态需要额外空间显示选择括号

        const width = markerWidth;
        const height = markerHeight + padding;
        canvas.width = width;
        canvas.height = height;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // 清除之前的内容
        ctx.clearRect(0, 0, width, height);

        // 缩放系数
        const scale = markerWidth / 27; // 基于原来27px宽度的缩放系数

        // 计算垂直偏移量，确保图标在正确的位置
        const verticalOffset = isSelected ? padding / 2 : 0;

        // 如果是选中状态，绘制顶部选择括号
        if (isSelected) {
            ctx.fillStyle = '#FF3B5C';

            // 顶部横条
            const bracketWidth = 30 * scale;
            ctx.fillRect((width - bracketWidth) / 2, 0, bracketWidth, 4);

            // 左侧竖条
            ctx.fillRect((width - bracketWidth) / 2, 0, 4, 8);

            // 右侧竖条
            ctx.fillRect(width - (width - bracketWidth) / 2 - 4, 0, 4, 8);
        }

        // 首先绘制阴影椭圆
        ctx.save();
        ctx.translate(3 * scale, (29 + verticalOffset) * scale);

        const shadowOpacity = 0.04;
        ctx.fillStyle = `rgba(0, 0, 0, ${shadowOpacity})`;

        // 绘制多个椭圆形成渐变阴影效果
        const drawEllipse = (cx, cy, rx, ry) => {
            ctx.beginPath();
            ctx.ellipse(cx * scale, cy, rx * scale, ry * scale, 0, 0, Math.PI * 2);
            ctx.fill();
        };

        // 从Marker.vue复制椭圆阴影
        const cx = 10.5;
        const cy = 5.8;
        drawEllipse(cx, cy, 10.5, 5.25);
        drawEllipse(cx, cy, 10.5, 5.25);
        drawEllipse(cx, cy, 9.5, 4.77);
        drawEllipse(cx, cy, 8.5, 4.29);
        drawEllipse(cx, cy, 7.5, 3.81);
        drawEllipse(cx, cy, 6.5, 3.34);
        drawEllipse(cx, cy, 5.5, 2.86);
        drawEllipse(cx, cy, 4.5, 2.38);

        ctx.restore();

        // 检查是否为特殊状态：已取件(pickedUp)的取货订单或已送达(delivered)的任何订单或已完成的订单
        // 对于配送订单(DELIVERY)，即使状态为pickedUp，也不应该显示为特殊状态
        const isSpecialStatus = isAssigned && ((orderStatus === 'pickedUp' && orderType !== 'DELIVERY') || orderStatus === 'delivered' || orderStatus === 'completed');

        // 记录状态信息，帮助调试
        if (orderStatus === 'pickedUp' || orderStatus === 'delivered') {
            console.log(`创建标记图标 - 检测到${orderStatus}状态: ID=${id}, 类型=${orderType}, 是否特殊状态=${isSpecialStatus}`);
            console.log(`特殊状态条件: isAssigned=${isAssigned}, orderStatus=${orderStatus}, orderType=${orderType}`);
            console.log(`特殊状态计算: ${isAssigned && ((orderStatus === 'pickedUp' && orderType === 'PICKUP') || orderStatus === 'delivered' || orderStatus === 'completed')}`);
        }

        // 检查是否为异常状态
        const isExceptionStatus = orderStatus === 'exceptional'; // 修正状态字符串

        // 检查是否为关闭状态
        const isClosedStatus = orderStatus === 'closed';

        // 绘制主标记形状
        if (isSpecialStatus) {
            // 特殊状态：白色填充
            ctx.fillStyle = '#FFFFFF';
        } else {
            // 普通状态：使用原始颜色
            ctx.fillStyle = color;
        }

        ctx.beginPath();

        // 使用与Marker.vue相同的路径数据绘制标记，但应用缩放
        ctx.save();
        ctx.scale(scale, scale);
        ctx.translate(0, verticalOffset / scale);

        const path = new Path2D('M27,13.5 C27,19.074644 20.250001,27.000002 14.75,34.500002 C14.016665,35.500004 12.983335,35.500004 12.25,34.500002 C6.7499993,27.000002 0,19.222562 0,13.5 C0,6.0441559 6.0441559,0 13.5,0 C20.955844,0 27,6.0441559 27,13.5 Z');
        ctx.fill(path);

        // 为特殊状态绘制原始颜色的边框
        if (isSpecialStatus) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 1.5;
            ctx.stroke(path);
        }

        // 绘制标记外边框
        ctx.fillStyle = 'rgba(0, 0, 0, 0.25)';
        const outlinePath = new Path2D('M13.5,0 C6.0441559,0 0,6.0441559 0,13.5 C0,19.222562 6.7499993,27 12.25,34.5 C13,35.522727 14.016664,35.500004 14.75,34.5 C20.250001,27 27,19.074644 27,13.5 C27,6.0441559 20.955844,0 13.5,0 Z M13.5,1 C20.415404,1 26,6.584596 26,13.5 C26,15.898657 24.495584,19.181431 22.220703,22.738281 C19.945823,26.295132 16.705119,30.142167 13.943359,33.908203 C13.743445,34.180814 13.612715,34.322738 13.5,34.441406 C13.387285,34.322738 13.256555,34.180814 13.056641,33.908203 C10.284481,30.127985 7.4148684,26.314159 5.015625,22.773438 C2.6163816,19.232715 1,15.953538 1,13.5 C1,6.584596 6.584596,1 13.5,1 Z');
        ctx.fill(outlinePath);
        ctx.restore();

        // 添加未分配标记的白色圆圈
        if (!isAssigned) {
            ctx.save();

            // 移动到正确位置
            ctx.translate(8 * scale, (8 + verticalOffset) * scale);

            // 绘制黑色背景圆
            ctx.fillStyle = 'rgba(0, 0, 0, 0.25)';
            ctx.beginPath();
            ctx.arc(5.5 * scale, 5.5 * scale, 5.5 * scale, 0, Math.PI * 2);
            ctx.fill();

            // 绘制白色前景圆
            ctx.fillStyle = '#FFFFFF';
            ctx.beginPath();
            ctx.arc(5.5 * scale, 5.5 * scale, 5.5 * scale, 0, Math.PI * 2);
            ctx.fill();

            ctx.restore();
        }

        // 添加P角标 - 为所有取货订单添加小角标
        const isPickupType = orderType === 'PICKUP';
        if (isPickupType && !isSpecialStatus) {
            ctx.save();

            // 右上角小圆形位置
            const tagX = width - 8 * scale;
            const tagY = 8 * scale + verticalOffset;
            const tagRadius = 5 * scale;

            // 绘制黑色圆形背景
            ctx.fillStyle = '#333333';
            ctx.beginPath();
            ctx.arc(tagX, tagY, tagRadius, 0, Math.PI * 2);
            ctx.fill();

            // 绘制白色P文字
            ctx.fillStyle = '#FFFFFF';
            ctx.font = `bold ${6 * scale}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('P', tagX, tagY);

            ctx.restore();
        }

        // 只有在 stopNumber 不为空且订单已分配时绘制文字，且不是异常或关闭状态
        if (isAssigned && stopNumber && stopNumber.trim() !== '' && !isExceptionStatus && !isClosedStatus) {
            if (isSpecialStatus) {
                // 特殊状态（已取件/已送达）- 黑色文字
                ctx.fillStyle = '#333333';
                ctx.font = `bold ${12 * scale}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                // 已分配标记文字稍微靠上一点
                ctx.fillText(stopNumber, width / 2, 12 * scale + verticalOffset);
            } else {
                // 普通已分配标记文字 - 白色文字
                ctx.fillStyle = '#FFFFFF';
                ctx.font = `bold ${12 * scale}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                // 已分配标记文字稍微靠上一点
                ctx.fillText(stopNumber, width / 2, 12 * scale + verticalOffset);
            }
        }

        // 针对异常状态(exception)绘制感叹号标记
        if (isExceptionStatus) {
            ctx.save();

            // 在标记中心位置绘制圆形背景（与停靠点编号相同位置）
            const centerX = width / 2;
            const centerY = 12 * scale + verticalOffset;
            const iconRadius = 9 * scale;

            // 绘制红色圆形背景
            ctx.fillStyle = '#FF3B30';
            ctx.beginPath();
            ctx.arc(centerX, centerY, iconRadius, 0, Math.PI * 2);
            ctx.fill();

            // 绘制白色感叹号
            ctx.fillStyle = '#FFFFFF';
            ctx.font = `bold ${14 * scale}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('!', centerX, centerY);

            ctx.restore();
        }

        // 针对关闭状态(closed)绘制X标记
        if (isClosedStatus) {
            ctx.save();

            // 在标记中心位置绘制圆形背景（与停靠点编号相同位置）
            const centerX = width / 2;
            const centerY = 12 * scale + verticalOffset;
            const iconRadius = 9 * scale;

            // 绘制灰色圆形背景
            ctx.fillStyle = '#8E8E93';
            ctx.beginPath();
            ctx.arc(centerX, centerY, iconRadius, 0, Math.PI * 2);
            ctx.fill();

            // 绘制白色X
            ctx.strokeStyle = '#FFFFFF';
            ctx.lineWidth = 2.5 * scale;
            ctx.beginPath();
            // 绘制X的第一条线（左上到右下）
            ctx.moveTo(centerX - 5 * scale, centerY - 5 * scale);
            ctx.lineTo(centerX + 5 * scale, centerY + 5 * scale);
            // 绘制X的第二条线（右上到左下）
            ctx.moveTo(centerX + 5 * scale, centerY - 5 * scale);
            ctx.lineTo(centerX - 5 * scale, centerY + 5 * scale);
            ctx.stroke();

            ctx.restore();
        }

        // 如果是选中状态，绘制底部选择括号
        if (isSelected) {
            ctx.fillStyle = '#FF3B5C';

            // 底部横条
            const bracketWidth = 30 * scale;
            ctx.fillRect((width - bracketWidth) / 2, height - 4, bracketWidth, 4);

            // 左侧竖条
            ctx.fillRect((width - bracketWidth) / 2, height - 8, 4, 8);

            // 右侧竖条
            ctx.fillRect(width - (width - bracketWidth) / 2 - 4, height - 8, 4, 8);
        }

        // 将画布转换为图像并添加到地图
        map.value.addImage(id, { width, height, data: ctx.getImageData(0, 0, width, height).data });

        // 将图标ID添加到集合
        createdIcons.value.add(id);
    };

    // 添加标记图标到地图
    const addMarkerImages = () => {
        if (!map.value) return;

        // 首先清理旧的自定义图标
        clearCustomIcons();

        // 创建默认的基础图标
        createSVGMarkerImage('unassigned-marker', '#9E9E9E', false, false, '', null, null);
        createSVGMarkerImage('assigned-marker', '#3B82F6', true, false, '', null, null);
        createSVGMarkerImage('selected-unassigned-marker', '#9E9E9E', false, true, '', null, null);
        createSVGMarkerImage('selected-assigned-marker', '#3B82F6', true, true, '', null, null);

        // 将基本图标添加到集合
        createdIcons.value.add('unassigned-marker');
        createdIcons.value.add('assigned-marker');
        createdIcons.value.add('selected-unassigned-marker');
        createdIcons.value.add('selected-assigned-marker');

        console.log('已创建所有状态的基础标记图标');
    };

    // 更新单个标记
    const updateMarker = (order, forceUpdate = false) => {
        if (!map.value || !map.value.getSource('orders-source') || !order || !order.id) {
            console.warn('无法更新标记：地图未加载或订单数据无效');
            return;
        }

        console.log('更新单个订单标记:', order.id, '停靠点编号:', order.stop_no, '状态:', order.status, '类型:', order.type, forceUpdate ? '(强制更新)' : '');

        // 如果是强制更新或订单状态为 pickedUp 或 delivered，先尝试删除旧的图标
        if (forceUpdate || order.status === 'pickedUp' || order.status === 'delivered') {
            // 记录状态变更日志
            if (order.status === 'pickedUp' || order.status === 'delivered') {
                console.log(`检测到订单状态变更为 ${order.status}，强制更新标记图标 (ID: ${order.id}, 类型: ${order.type})`);
            }

            // 构建可能的图标ID前缀
            const iconIdPrefix = `${order.id}-`;

            // 查找并删除所有与该订单相关的图标
            createdIcons.value.forEach(iconId => {
                if (iconId.includes(iconIdPrefix)) {
                    try {
                        if (map.value.hasImage(iconId)) {
                            map.value.removeImage(iconId);
                            console.log(`已删除图标: ${iconId}`);
                        }
                    } catch (error) {
                        console.error(`删除图标失败: ${iconId}`, error);
                    }
                }
            });
        }

        try {
            // 获取当前数据源中的所有特征
            const source = map.value.getSource('orders-source');
            const currentData = source._data || { type: 'FeatureCollection', features: [] };

            // 查找当前特征中是否存在该订单
            const featureIndex = currentData.features.findIndex(f => f.properties.id === order.id);

            // 获取坐标
            const coords = order.lng_lat || order.location;
            if (!coords || !Array.isArray(coords) || coords.length !== 2) {
                console.warn('订单坐标无效:', order.id);
                return;
            }

            // 注意：我们的坐标是 [lat, lng] 格式，而 GeoJSON 需要 [lng, lat] 格式
            const [lat, lng] = coords;
            if (isNaN(lat) || isNaN(lng)) {
                console.warn('订单坐标格式无效:', order.id);
                return;
            }

            // 检查订单是否已分配
            const isAssigned = !!order.driver_id;

            // 获取停靠点编号
            let stopNo = '';
            if (isAssigned) {
                // 优先使用stop_no属性
                if (order.stop_no !== undefined && order.stop_no !== null) {
                    stopNo = String(order.stop_no);
                    console.log(`订单 ${order.id} 使用stop_no: ${stopNo}`);
                }
                // 备选：使用pickup_stop_no属性
                else if (order.pickup_stop_no) {
                    stopNo = String(order.pickup_stop_no);
                    console.log(`订单 ${order.id} 使用pickup_stop_no: ${stopNo}`);
                }
                // 如果两个都没有但有订单号，显示订单号最后一位
                else if (order.no) {
                    const orderNo = String(order.no);
                    stopNo = orderNo.slice(-1);
                    console.log(`订单 ${order.id} 使用订单号最后一位: ${stopNo}`);
                }
            } else {
                // 未分配订单不显示停靠点编号
                stopNo = '';
                console.log(`未分配订单 ${order.id} 不显示停靠点编号`);
            }

            // 获取司机颜色
            const driverColor = isAssigned ? getMarkerColor(order) : '#9E9E9E';

            // 检查是否为选中的订单
            const isSelected = orderStore.selectedOrderIds.has(order.id);

            // 为每个订单创建特定的图标ID，包含停靠点编号
            let iconId;

            // 构建基于订单ID和停靠点编号的唯一图标ID
            const uniqIconSuffix = `${order.id}-${stopNo || '0'}`;

            if (isSelected) {
                // 选中状态的图标ID
                iconId = isAssigned
                    ? `selected-assigned-${uniqIconSuffix}`
                    : `selected-unassigned-${uniqIconSuffix}`;
            } else {
                // 非选中状态的图标ID
                iconId = isAssigned
                    ? `assigned-${uniqIconSuffix}`
                    : `unassigned-${uniqIconSuffix}`;
            }

            // 强制重新创建图标，确保停靠点编号正确显示
            if (forceUpdate || order.status === 'pickedUp' || order.status === 'delivered' || map.value.hasImage(iconId)) {
                try {
                    // 如果是强制更新或状态变更，尝试删除所有可能的图标
                    if (forceUpdate || order.status === 'pickedUp' || order.status === 'delivered') {
                        // 构建可能的图标ID
                        const possibleIconIds = [
                            `selected-assigned-${uniqIconSuffix}`,
                            `selected-unassigned-${uniqIconSuffix}`,
                            `assigned-${uniqIconSuffix}`,
                            `unassigned-${uniqIconSuffix}`
                        ];

                        // 尝试删除这些图标
                        possibleIconIds.forEach(id => {
                            if (map.value.hasImage(id)) {
                                try {
                                    map.value.removeImage(id);
                                    console.log(`已删除图标: ${id}`);
                                } catch (err) {
                                    console.error(`删除图标失败: ${id}`, err);
                                }
                            }
                        });
                    } else if (map.value.hasImage(iconId)) {
                        // 如果不是强制更新或状态变更，只删除当前图标
                        map.value.removeImage(iconId);
                    }
                } catch (error) {
                    console.error(`删除图标失败: ${iconId}`, error);
                }
            }

            // 创建图标
            createSVGMarkerImage(
                iconId,
                isAssigned ? driverColor : '#9E9E9E',
                isAssigned,
                isSelected,
                stopNo,
                order.status,  // 添加订单状态参数
                order.type && order.type.toUpperCase()  // 添加订单类型参数
            );

            // 将图标ID添加到集合
            createdIcons.value.add(iconId);

            // 创建新的GeoJSON特征对象
            const newFeature = {
                type: 'Feature',
                geometry: {
                    type: 'Point',
                    coordinates: [lng, lat]
                },
                properties: {
                    id: order.id,
                    status: isAssigned ? 'assigned' : 'unassigned',
                    stopNo: stopNo,
                    driverId: order.driver_id || null,
                    routeId: order.route_id || null,
                    selected: isSelected,
                    iconImage: iconId,  // 使用为每个订单创建的唯一图标ID
                    color: driverColor,
                    zIndexOffset: isSelected ? 1000 : (isAssigned ? 0 : 500) // 优先显示未分配订单：选中(1000) > 未分配(500) > 已分配(0)
                }
            };

            // 如果特征已存在，则更新它
            if (featureIndex !== -1) {
                currentData.features[featureIndex] = newFeature;
            } else {
                // 否则添加新特征
                currentData.features.push(newFeature);
            }

            // 更新数据源
            source.setData(currentData);

            // 确保标记图层在最顶部
            moveSymbolLayersToTop();

            console.log('单个标记更新完成:', order.id, '停靠点编号:', stopNo);
        } catch (error) {
            console.error('更新单个标记时出错:', error);
        }
    };

    // 查找同一位置的订单并分组
    const groupOrdersByLocation = (orders) => {
        const locationGroups = new Map();

        orders.forEach(order => {
            // 获取坐标
            const coords = order.lng_lat || order.location;
            if (!coords || !Array.isArray(coords) || coords.length !== 2) {
                return;
            }

            const [lat, lng] = coords;
            if (isNaN(lat) || isNaN(lng)) {
                return;
            }

            // 使用坐标作为键，但保留一定精度（小数点后5位，约1.1米精度）
            const locationKey = `${parseFloat(lat).toFixed(5)},${parseFloat(lng).toFixed(5)}`;

            if (!locationGroups.has(locationKey)) {
                locationGroups.set(locationKey, []);
            }

            locationGroups.get(locationKey).push(order);
        });

        return locationGroups;
    };

    // 创建聚合标记图标 - 与普通订单标记完全一致，只是在左上角添加数量角标
    const createClusterMarkerImage = (id, count, primaryColor, hasSelected, stopNo = '', status = '', type = '', isAssigned = false) => {
        if (!map.value) return;

        // 检查图标是否已存在
        if (map.value.hasImage(id)) return;

        // 创建一个Canvas元素来绘制SVG
        const canvas = document.createElement('canvas');

        // 设置正确的Canvas尺寸 - 与普通订单标记完全一致
        const markerWidth = 32; // 标记宽度
        const markerHeight = 48; // 标记高度
        const padding = hasSelected ? 16 : 0; // 选中状态需要额外空间显示选择括号

        const width = markerWidth;
        const height = markerHeight + padding;
        canvas.width = width;
        canvas.height = height;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // 清除之前的内容
        ctx.clearRect(0, 0, width, height);

        // 缩放系数
        const scale = markerWidth / 27; // 基于原来27px宽度的缩放系数

        // 计算垂直偏移量，确保图标在正确的位置
        const verticalOffset = hasSelected ? padding / 2 : 0;

        // 如果是选中状态，绘制顶部选择括号
        if (hasSelected) {
            ctx.fillStyle = '#FF3B5C';

            // 顶部横条
            const bracketWidth = 30 * scale;
            ctx.fillRect((width - bracketWidth) / 2, 0, bracketWidth, 4);

            // 左侧竖条
            ctx.fillRect((width - bracketWidth) / 2, 0, 4, 8);

            // 右侧竖条
            ctx.fillRect(width - (width - bracketWidth) / 2 - 4, 0, 4, 8);
        }

        // 首先绘制阴影椭圆
        ctx.save();
        ctx.translate(3 * scale, (29 + verticalOffset) * scale);

        const shadowOpacity = 0.04;
        ctx.fillStyle = `rgba(0, 0, 0, ${shadowOpacity})`;

        // 绘制多个椭圆形成渐变阴影效果
        const drawEllipse = (cx, cy, rx, ry) => {
            ctx.beginPath();
            ctx.ellipse(cx * scale, cy, rx * scale, ry * scale, 0, 0, Math.PI * 2);
            ctx.fill();
        };

        // 从Marker.vue复制椭圆阴影
        const cx = 10.5;
        const cy = 5.8;
        drawEllipse(cx, cy, 10.5, 5.25);
        drawEllipse(cx, cy, 10.5, 5.25);
        drawEllipse(cx, cy, 9.5, 4.77);
        drawEllipse(cx, cy, 8.5, 4.29);
        drawEllipse(cx, cy, 7.5, 3.81);
        drawEllipse(cx, cy, 6.5, 3.34);
        drawEllipse(cx, cy, 5.5, 2.86);
        drawEllipse(cx, cy, 4.5, 2.38);

        ctx.restore();

        // 检查是否为特殊状态：已取件(pickedUp)的取货订单或已送达(delivered)的任何订单或已完成的订单
        // 对于配送订单(DELIVERY)，即使状态为pickedUp，也不应该显示为特殊状态
        const isSpecialStatus = (status === 'pickedUp' && type !== 'DELIVERY') || status === 'delivered' || status === 'completed';
        console.log(`聚合标记特殊状态判断: status=${status}, type=${type}, isSpecialStatus=${isSpecialStatus}`);

        // 检查是否为异常状态
        const isExceptionStatus = status === 'exceptional';

        // 检查是否为关闭状态
        const isClosedStatus = status === 'closed';

        // 绘制主标记形状
        if (isSpecialStatus) {
            // 特殊状态：白色填充
            ctx.fillStyle = '#FFFFFF';
        } else {
            // 普通状态：使用原始颜色
            ctx.fillStyle = primaryColor;
        }

        ctx.beginPath();

        // 使用与Marker.vue相同的路径数据绘制标记，但应用缩放
        ctx.save();
        ctx.scale(scale, scale);
        ctx.translate(0, verticalOffset / scale);

        const path = new Path2D('M27,13.5 C27,19.074644 20.250001,27.000002 14.75,34.500002 C14.016665,35.500004 12.983335,35.500004 12.25,34.500002 C6.7499993,27.000002 0,19.222562 0,13.5 C0,6.0441559 6.0441559,0 13.5,0 C20.955844,0 27,6.0441559 27,13.5 Z');
        ctx.fill(path);

        // 为特殊状态绘制原始颜色的边框
        if (isSpecialStatus) {
            ctx.strokeStyle = primaryColor;
            ctx.lineWidth = 1.5;
            ctx.stroke(path);
        }

        // 绘制标记外边框
        ctx.fillStyle = 'rgba(0, 0, 0, 0.25)';
        const outlinePath = new Path2D('M13.5,0 C6.0441559,0 0,6.0441559 0,13.5 C0,19.222562 6.7499993,27 12.25,34.5 C13,35.522727 14.016664,35.500004 14.75,34.5 C20.250001,27 27,19.074644 27,13.5 C27,6.0441559 20.955844,0 13.5,0 Z M13.5,1 C20.415404,1 26,6.584596 26,13.5 C26,15.898657 24.495584,19.181431 22.220703,22.738281 C19.945823,26.295132 16.705119,30.142167 13.943359,33.908203 C13.743445,34.180814 13.612715,34.322738 13.5,34.441406 C13.387285,34.322738 13.256555,34.180814 13.056641,33.908203 C10.284481,30.127985 7.4148684,26.314159 5.015625,22.773438 C2.6163816,19.232715 1,15.953538 1,13.5 C1,6.584596 6.584596,1 13.5,1 Z');
        ctx.fill(outlinePath);
        ctx.restore();

        // 添加未分配标记的白色圆圈 - 使用传入的 isAssigned 参数
        if (!isAssigned) {
            ctx.save();

            // 移动到正确位置
            ctx.translate(8 * scale, (8 + verticalOffset) * scale);

            // 绘制黑色背景圆
            ctx.fillStyle = 'rgba(0, 0, 0, 0.25)';
            ctx.beginPath();
            ctx.arc(5.5 * scale, 5.5 * scale, 5.5 * scale, 0, Math.PI * 2);
            ctx.fill();

            // 绘制白色前景圆
            ctx.fillStyle = '#FFFFFF';
            ctx.beginPath();
            ctx.arc(5.5 * scale, 5.5 * scale, 5.5 * scale, 0, Math.PI * 2);
            ctx.fill();

            ctx.restore();

            console.log('为聚合标记添加未分配标记的白色圆圈');
        }

        // 添加P角标 - 为所有取货订单添加小角标
        const isPickupType = type === 'PICKUP';
        if (isPickupType && !isSpecialStatus) {
            ctx.save();

            // 右上角小圆形位置
            const tagX = width - 8 * scale;
            const tagY = 8 * scale + verticalOffset;
            const tagRadius = 5 * scale;

            // 绘制黑色圆形背景
            ctx.fillStyle = '#333333';
            ctx.beginPath();
            ctx.arc(tagX, tagY, tagRadius, 0, Math.PI * 2);
            ctx.fill();

            // 绘制白色P文字
            ctx.fillStyle = '#FFFFFF';
            ctx.font = `bold ${6 * scale}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('P', tagX, tagY);

            ctx.restore();
        }

        // 只有在 stopNo 不为空且不是异常或关闭状态时绘制文字
        if (stopNo && stopNo.trim() !== '' && !isExceptionStatus && !isClosedStatus) {
            if (isSpecialStatus) {
                // 特殊状态（已取件/已送达）- 黑色文字
                ctx.fillStyle = '#333333';
                ctx.font = `bold ${12 * scale}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                // 文字稍微靠上一点
                ctx.fillText(stopNo, width / 2, 12 * scale + verticalOffset);
            } else {
                // 普通状态 - 白色文字
                ctx.fillStyle = '#FFFFFF';
                ctx.font = `bold ${12 * scale}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                // 文字稍微靠上一点
                ctx.fillText(stopNo, width / 2, 12 * scale + verticalOffset);
            }
        }

        // 针对异常状态(exceptional)绘制感叹号标记
        if (isExceptionStatus) {
            ctx.save();

            // 在标记中心位置绘制圆形背景（与停靠点编号相同位置）
            const centerX = width / 2;
            const centerY = 12 * scale + verticalOffset;
            const iconRadius = 9 * scale;

            // 绘制红色圆形背景
            ctx.fillStyle = '#FF3B30';
            ctx.beginPath();
            ctx.arc(centerX, centerY, iconRadius, 0, Math.PI * 2);
            ctx.fill();

            // 绘制白色感叹号
            ctx.fillStyle = '#FFFFFF';
            ctx.font = `bold ${14 * scale}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('!', centerX, centerY);

            ctx.restore();
        }

        // 针对关闭状态(closed)绘制X标记
        if (isClosedStatus) {
            ctx.save();

            // 在标记中心位置绘制圆形背景（与停靠点编号相同位置）
            const centerX = width / 2;
            const centerY = 12 * scale + verticalOffset;
            const iconRadius = 9 * scale;

            // 绘制灰色圆形背景
            ctx.fillStyle = '#8E8E93';
            ctx.beginPath();
            ctx.arc(centerX, centerY, iconRadius, 0, Math.PI * 2);
            ctx.fill();

            // 绘制白色X
            ctx.strokeStyle = '#FFFFFF';
            ctx.lineWidth = 2.5 * scale;
            ctx.beginPath();
            // 绘制X的第一条线（左上到右下）
            ctx.moveTo(centerX - 5 * scale, centerY - 5 * scale);
            ctx.lineTo(centerX + 5 * scale, centerY + 5 * scale);
            // 绘制X的第二条线（右上到左下）
            ctx.moveTo(centerX + 5 * scale, centerY - 5 * scale);
            ctx.lineTo(centerX - 5 * scale, centerY + 5 * scale);
            ctx.stroke();

            ctx.restore();
        }

        // 如果是选中状态，绘制底部选择括号
        if (hasSelected) {
            ctx.fillStyle = '#FF3B5C';

            // 底部横条
            const bracketWidth = 30 * scale;
            ctx.fillRect((width - bracketWidth) / 2, height - 4, bracketWidth, 4);

            // 左侧竖条
            ctx.fillRect((width - bracketWidth) / 2, height - 8, 4, 8);

            // 右侧竖条
            ctx.fillRect(width - (width - bracketWidth) / 2 - 4, height - 8, 4, 8);
        }

        // 在左上角添加订单数量角标
        const badgeRadius = 7;
        const badgeX = 8;
        const badgeY = 8;

        // 绘制角标背景
        ctx.fillStyle = '#FF3B5C'; // 红色角标
        ctx.beginPath();
        ctx.arc(badgeX, badgeY, badgeRadius, 0, Math.PI * 2);
        ctx.fill();

        // 绘制订单数量
        ctx.fillStyle = '#FFFFFF';
        ctx.font = 'bold 9px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // 显示订单数量，超过9显示9+
        const countText = count > 9 ? '9+' : count.toString();
        ctx.fillText(countText, badgeX, badgeY);

        // 将画布转换为图像并添加到地图
        try {
            const imageData = ctx.getImageData(0, 0, width, height);
            map.value.addImage(id, {
                width,
                height,
                data: new Uint8Array(imageData.data.buffer)
            });

            // 将图标ID添加到集合
            createdIcons.value.add(id);

            console.log(`已创建聚合标记图标: ${id}，订单数量: ${count}`);
        } catch (error) {
            console.error(`创建聚合标记图标失败: ${id}`, error);

            // 如果发生错误，尝试简化版本
            try {
                // 清除Canvas并重新绘制简单版本
                ctx.clearRect(0, 0, width, height);
                ctx.fillStyle = primaryColor;

                // 简单圆形标记
                ctx.beginPath();
                ctx.arc(width / 2, height / 2, 12, 0, Math.PI * 2);
                ctx.fill();

                if (hasSelected) {
                    ctx.strokeStyle = '#FF3B5C';
                    ctx.lineWidth = 3;
                    ctx.stroke();
                }

                // 在左上角添加订单数量角标
                ctx.fillStyle = '#FF3B5C';
                ctx.beginPath();
                ctx.arc(badgeX, badgeY, badgeRadius, 0, Math.PI * 2);
                ctx.fill();

                // 绘制订单数量
                ctx.fillStyle = '#FFFFFF';
                ctx.font = 'bold 9px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(countText, badgeX, badgeY);

                // 获取简化图像数据
                const simpleImageData = ctx.getImageData(0, 0, width, height);

                // 尝试添加简化版本的图标
                map.value.addImage(id, {
                    width: width,
                    height: height,
                    data: new Uint8Array(simpleImageData.data.buffer),
                    pixelRatio: 1
                });

                console.log(`使用简化版本创建聚合标记图标: ${id}`);
            } catch (fallbackError) {
                console.error(`创建聚合标记图标完全失败: ${id}`, fallbackError);
            }
        }
    };

    // 更新图层中订单的数据
    const updateOrdersData = (orders) => {
        if (!map.value || !map.value.getSource('orders-source')) return;

        console.log('更新订单数据，数量:', orders.length);
        // 过滤有效订单
        const validOrders = orders.filter(order => order && (order.lng_lat || order.location));

        // 按位置分组订单
        const orderGroups = groupOrdersByLocation(validOrders);
        console.log(`订单按位置分组后共有 ${orderGroups.size} 个位置点`);

        // 创建GeoJSON特征
        const features = [];

        // 处理每个位置组
        orderGroups.forEach((ordersAtLocation, locationKey) => {
            // 如果该位置只有一个订单，正常处理
            if (ordersAtLocation.length === 1) {
                const order = ordersAtLocation[0];

                // 获取坐标
                const coords = order.lng_lat || order.location;
                const [lat, lng] = coords;

                // 检查订单是否已分配
                const isAssigned = !!order.driver_id;

                // 获取停靠点编号
                let stopNo = '';
                if (isAssigned) {
                    // 优先使用stop_no属性
                    if (order.stop_no) {
                        stopNo = String(order.stop_no);
                    }
                    // 备选：使用pickup_stop_no属性
                    else if (order.pickup_stop_no) {
                        stopNo = String(order.pickup_stop_no);
                    }
                    // 如果两个都没有但有订单号，显示订单号最后一位
                    else if (order.no) {
                        const orderNo = String(order.no);
                        stopNo = orderNo.slice(-1);
                    }
                } else {
                    // 未分配订单不显示停靠点编号
                    stopNo = '';
                }

                // 获取司机颜色
                const driverColor = isAssigned ? getMarkerColor(order) : '#9E9E9E';

                // 检查是否为选中的订单
                const isSelected = orderStore.selectedOrderIds.has(order.id);

                // 为每个订单创建特定的图标ID，包含停靠点编号
                let iconId;

                // 构建基于订单ID和停靠点编号的唯一图标ID
                const uniqIconSuffix = `${order.id}-${stopNo || '0'}`;

                if (isSelected) {
                    // 选中状态的图标ID
                    iconId = isAssigned
                        ? `selected-assigned-${uniqIconSuffix}`
                        : `selected-unassigned-${uniqIconSuffix}`;
                } else {
                    // 非选中状态的图标ID
                    iconId = isAssigned
                        ? `assigned-${uniqIconSuffix}`
                        : `unassigned-${uniqIconSuffix}`;
                }

                // 如果该图标不存在，则创建它
                if (!map.value.hasImage(iconId)) {
                    // 创建图标
                    createSVGMarkerImage(
                        iconId,
                        isAssigned ? driverColor : '#9E9E9E',
                        isAssigned,
                        isSelected,
                        stopNo,
                        order.status,  // 添加订单状态参数
                        order.type && order.type.toUpperCase()  // 添加订单类型参数
                    );

                    // 将图标ID添加到集合
                    createdIcons.value.add(iconId);
                }

                // 创建GeoJSON特征对象
                features.push({
                    type: 'Feature',
                    geometry: {
                        type: 'Point',
                        coordinates: [lng, lat]
                    },
                    properties: {
                        id: order.id,
                        status: isAssigned ? 'assigned' : 'unassigned',
                        stopNo: stopNo,
                        driverId: order.driver_id || null,
                        routeId: order.route_id || null,
                        selected: isSelected,
                        iconImage: iconId,  // 使用为每个订单创建的唯一图标ID
                        color: driverColor,
                        zIndexOffset: isSelected ? 1000 : (isAssigned ? 0 : 500), // 优先显示未分配订单：选中(1000) > 未分配(500) > 已分配(0)
                        isCluster: false
                    }
                });
            } else {
                // 如果该位置有多个订单，创建聚合标记
                const [lat, lng] = locationKey.split(',').map(parseFloat);

                // 确定聚合标记的主要颜色 - 优先显示未分配订单
                const assignedOrders = ordersAtLocation.filter(o => !!o.driver_id);
                const unassignedOrders = ordersAtLocation.filter(o => !o.driver_id);
                const hasAssignedOrders = assignedOrders.length > 0;
                const hasUnassignedOrders = unassignedOrders.length > 0;

                // 检查是否有选中的订单
                const hasSelectedOrders = ordersAtLocation.some(o => orderStore.selectedOrderIds.has(o.id));

                // 确定主要颜色 - 优先显示未分配订单的样式
                let primaryColor = '#9E9E9E'; // 默认灰色（未分配）
                let isClusterAssigned = false; // 聚合标记是否显示为已分配样式

                if (hasUnassignedOrders) {
                    // 如果有未分配订单，优先显示未分配样式（灰色）
                    primaryColor = '#9E9E9E';
                    isClusterAssigned = false;
                } else if (hasAssignedOrders) {
                    // 只有当所有订单都已分配时，才显示已分配样式
                    primaryColor = getMarkerColor(assignedOrders[0]);
                    isClusterAssigned = true;
                }

                // 确定要显示的停靠点编号（使用第一个已分配订单的停靠点编号，如果没有则为空）
                let stopNo = '';
                let status = '';
                let type = '';

                // 优先使用未分配订单的信息
                if (hasUnassignedOrders) {
                    // 如果有未分配订单，优先使用第一个未分配订单的信息
                    const firstUnassignedOrder = unassignedOrders[0];

                    // 对于未分配订单，不显示停靠点编号
                    stopNo = '';

                    // 获取状态和类型
                    status = firstUnassignedOrder.status || '';
                    type = firstUnassignedOrder.type || '';

                    console.log(`聚合标记优先使用未分配订单信息: stopNo=${stopNo}, status=${status}, type=${type}`);
                } else if (hasAssignedOrders) {
                    // 只有当所有订单都已分配时，才使用已分配订单的信息
                    // 显示最小的 stop_no
                    const validStopNumbers = assignedOrders
                        .map(order => {
                            // 优先使用 stop_no
                            if (order.stop_no && !isNaN(order.stop_no)) {
                                return parseInt(order.stop_no);
                            }
                            // 备选：使用 pickup_stop_no
                            else if (order.pickup_stop_no && !isNaN(order.pickup_stop_no)) {
                                return parseInt(order.pickup_stop_no);
                            }
                            // 如果都没有，返回 null
                            return null;
                        })
                        .filter(num => num !== null); // 过滤掉无效值

                    if (validStopNumbers.length > 0) {
                        // 找到最小的停靠点编号
                        const minStopNo = Math.min(...validStopNumbers);
                        stopNo = String(minStopNo);
                        console.log(`聚合标记中的停靠点编号: [${validStopNumbers.join(', ')}]，显示最小值: ${stopNo}`);
                    } else {
                        // 如果没有有效的停靠点编号，尝试使用订单号的最后一位
                        const firstAssignedOrder = assignedOrders[0];
                        if (firstAssignedOrder.no) {
                            stopNo = firstAssignedOrder.no.slice(-1);
                        } else {
                            stopNo = '';
                        }
                        console.log(`聚合标记没有有效停靠点编号，使用备用方案: ${stopNo}`);
                    }

                    // 获取状态和类型（使用第一个已分配订单的信息）
                    const firstAssignedOrder = assignedOrders[0];
                    status = firstAssignedOrder.status || '';
                    type = firstAssignedOrder.type || '';

                    console.log(`聚合标记使用已分配订单信息（所有订单都已分配）: stopNo=${stopNo}, status=${status}, type=${type}`);
                } else if (ordersAtLocation.length > 0) {
                    // 备用方案：使用第一个订单的信息
                    const firstOrder = ordersAtLocation[0];

                    stopNo = '';
                    status = firstOrder.status || '';
                    type = firstOrder.type || '';

                    console.log(`聚合标记使用备用订单信息: stopNo=${stopNo}, status=${status}, type=${type}`);
                }

                // 创建聚合标记的唯一ID
                const clusterIconId = `cluster-${locationKey.replace(/\./g, '_')}-${ordersAtLocation.length}${hasSelectedOrders ? '-selected' : ''}`;

                // 创建聚合标记图标
                createClusterMarkerImage(
                    clusterIconId,
                    ordersAtLocation.length,
                    primaryColor,
                    hasSelectedOrders,
                    stopNo,
                    status,
                    type,
                    isClusterAssigned // 传递聚合标记是否显示为已分配样式
                );

                // 创建聚合标记的GeoJSON特征
                features.push({
                    type: 'Feature',
                    geometry: {
                        type: 'Point',
                        coordinates: [lng, lat]
                    },
                    properties: {
                        // 使用所有订单ID的连接作为聚合标记的ID
                        id: `cluster-${ordersAtLocation.map(o => o.id).join('-')}`,
                        status: hasUnassignedOrders ? 'unassigned' : 'assigned', // 优先显示未分配状态
                        iconImage: clusterIconId,
                        color: primaryColor,
                        selected: hasSelectedOrders,
                        zIndexOffset: hasSelectedOrders ? 1000 : (hasUnassignedOrders ? 500 : 0), // 优先显示未分配订单：选中(1000) > 未分配(500) > 已分配(0)
                        isCluster: true,
                        clusterCount: ordersAtLocation.length,
                        // 存储聚合中的所有订单ID，以便点击时处理
                        // 将数组转换为JSON字符串，确保在事件处理中能够正确解析
                        clusterOrderIds: JSON.stringify(ordersAtLocation.map(o => o.id))
                    }
                });
            }
        });

        // 打印一些聚合标记的信息，用于调试
        const clusterFeatures = features.filter(f => f.properties.isCluster);
        if (clusterFeatures.length > 0) {
            console.log(`创建了 ${clusterFeatures.length} 个聚合标记`);
            console.log('聚合标记示例:', clusterFeatures[0]);
        }

        // 更新数据源
        map.value.getSource('orders-source').setData({
            type: 'FeatureCollection',
            features: features
        });

        // 确保图层可见性和大小设置
        if (map.value.getLayer('unassigned-orders-layer') &&
            map.value.getLayer('assigned-orders-layer') &&
            map.value.getLayer('selected-orders-layer')) {

            // 确保图层可见
            map.value.setLayoutProperty('unassigned-orders-layer', 'visibility', 'visible');
            map.value.setLayoutProperty('assigned-orders-layer', 'visibility', 'visible');
            map.value.setLayoutProperty('selected-orders-layer', 'visibility', 'visible');

            // 根据当前缩放级别更新图标大小
            updateMarkerSizeForZoom();
        }

        // 更新性能统计
        performanceStats.value.markerCount = features.length;
        console.log('Canvas标记更新完成，总数:', features.length);

        // 确保标记图层在最顶部
        moveSymbolLayersToTop();
    };

    // 清除自定义图标
    const clearCustomIcons = () => {
        if (!map.value) return;

        // 保留基本图标
        const basicIcons = [
            'unassigned-marker',
            'assigned-marker',
            'selected-unassigned-marker',
            'selected-assigned-marker'
        ];

        // 删除所有非基本图标
        createdIcons.value.forEach(iconId => {
            if (!basicIcons.includes(iconId) && map.value.hasImage(iconId)) {
                try {
                    map.value.removeImage(iconId);
                } catch (error) {
                    console.error(`删除图标失败: ${iconId}`, error);
                }
            }
        });

        // 清空图标集合
        createdIcons.value.clear();

        // 重新添加基本图标到集合
        basicIcons.forEach(iconId => {
            createdIcons.value.add(iconId);
        });
    };

    // 根据地图缩放级别更新标记大小
    const updateMarkerSizeForZoom = () => {
        if (!map.value) return;

        const zoom = map.value.getZoom();

        // 根据缩放级别计算大小系数 - 设置范围为 0.8 到 1.2
        let size = 1.0; // 默认大小

        if (zoom <= 10) {
            size = 0.8; // 最小值
        } else if (zoom <= 12) {
            size = 0.9; // 中间值
        } else if (zoom <= 14) {
            size = 1.0; // 中间值
        } else if (zoom <= 16) {
            size = 1.1; // 中间值
        } else {
            size = 1.2; // 最大值
        }

        // 更新图层图标大小
        ['unassigned-orders-layer', 'assigned-orders-layer', 'selected-orders-layer'].forEach(layerId => {
            if (map.value.getLayer(layerId)) {
                map.value.setLayoutProperty(layerId, 'icon-size', size);
            }
        });
    };

    // 确保标记图层显示在所有其他图层之上
    const moveSymbolLayersToTop = () => {
        if (!map.value) return;

        try {
            // 首先移动司机位置图层（如果存在）
            if (map.value.getLayer('drivers-layer')) {
                map.value.moveLayer('drivers-layer');
            }

            // 然后移动订单图层，这样订单图层将在司机图层之上
            const symbolLayerIds = [
                'unassigned-orders-layer',
                'assigned-orders-layer',
                'selected-orders-layer'
            ];

            // 检查每个图层是否存在，并移动到最顶层
            symbolLayerIds.forEach(layerId => {
                if (map.value.getLayer(layerId)) {
                    map.value.moveLayer(layerId);
                }
            });
        } catch (error) {
            console.error('移动Symbol图层到顶层时出错:', error);
        }
    };

    // 点击标记时的处理函数
    const handleSymbolClick = (e) => {
        if (!map.value) return;

        console.log('标记点击事件触发');

        // 查询所有订单相关的图层
        const features = map.value.queryRenderedFeatures(e.point, {
            layers: [
                'unassigned-orders-layer',
                'assigned-orders-layer',
                'selected-orders-layer'
            ]
        });

        // 如果没有找到特征，或者点击的不是订单，则返回
        if (!features.length) {
            console.log('点击位置没有找到特征');
            return;
        }

        // 阻止事件冒泡（防止事件穿透到地图）
        if (e && e.originalEvent) {
            e.originalEvent.preventDefault();
            e.originalEvent.stopPropagation();
        }

        const feature = features[0];
        console.log('点击的特征:', feature);

        // 确保 properties 存在且包含 id
        if (!feature.properties || typeof feature.properties.id === 'undefined') {
             console.warn('Clicked feature is missing properties or id:', feature);
             return;
        }

        // 检查是否是聚合标记
        const isCluster = feature.properties.isCluster;
        console.log('是否是聚合标记:', isCluster);

        // 防抖处理点击
        const now = Date.now();
        if (now - lastClickTime.value < clickDebounceTime) {
            console.log('点击过于频繁，忽略此次点击');
            return;
        }
        lastClickTime.value = now;

        // 输出更多调试信息
        console.log('处理点击事件，是否是聚合标记:', isCluster);
        console.log('特征属性:', feature.properties);

        if (isCluster) {
            // 处理聚合标记点击
            console.log('点击了聚合标记，包含订单数量:', feature.properties.clusterCount);

            // 获取聚合中的所有订单ID
            let clusterOrderIds = feature.properties.clusterOrderIds;
            console.log('原始聚合标记中的订单ID:', clusterOrderIds, '类型:', typeof clusterOrderIds);

            // 检查是否是字符串，如果是，尝试解析为数组
            if (typeof clusterOrderIds === 'string') {
                try {
                    clusterOrderIds = JSON.parse(clusterOrderIds);
                    console.log('解析后的聚合订单ID:', clusterOrderIds);
                } catch (e) {
                    // 如果不是有效的JSON，尝试按逗号分割
                    clusterOrderIds = clusterOrderIds.split(',');
                    console.log('按逗号分割后的聚合订单ID:', clusterOrderIds);
                }
            }

            if (!clusterOrderIds || !Array.isArray(clusterOrderIds) || clusterOrderIds.length === 0) {
                console.warn('聚合标记缺少有效的订单ID列表');
                return;
            }

            // 检查是否按下了Ctrl键（多选模式）
            const isMultiSelect = e.originalEvent && (e.originalEvent.ctrlKey || e.originalEvent.metaKey);
            console.log('聚合标记点击 - 检测Ctrl键:', isMultiSelect);

            // 获取聚合中的有效订单（排除closed和exceptional状态的未分配订单）
            const validOrderIds = clusterOrderIds.filter(id => {
                const order = orderStore.allOrders.find(o => o.id === id);
                if (!order) {
                    console.log(`未找到ID为 ${id} 的订单`);
                    return false;
                }

                // 排除未分配的closed或exceptional订单
                if (!order.driver_id && (order.status === 'closed' || order.status === 'exceptional')) {
                    console.log(`订单 ${id} 状态为 ${order.status}，不允许选择`);
                    return false;
                }

                return true;
            });

            console.log('聚合标记中的有效订单ID:', validOrderIds);

            if (validOrderIds.length === 0) {
                console.log('聚合标记中没有可选择的有效订单');
                return;
            }

            if (isMultiSelect) {
                // Ctrl键多选模式：切换所有订单的选中状态
                validOrderIds.forEach(id => {
                    orderStore.toggleOrderSelection(id);
                });
                console.log('多选模式：切换所有订单的选中状态');
            } else {
                // 单选模式：检查是否所有订单都已选中
                const allSelected = validOrderIds.every(id => orderStore.selectedOrderIds.has(id));

                if (allSelected) {
                    // 如果所有订单都已选中，则清空选择
                    orderStore.clearSelection();
                    console.log('单选模式：所有订单都已选中，清空选择');
                } else {
                    // 否则，清空当前选择，然后选中所有聚合中的订单
                    orderStore.clearSelection();
                    validOrderIds.forEach(id => {
                        orderStore.toggleOrderSelection(id);
                    });
                    console.log('单选模式：选中所有聚合中的订单');
                }
            }

            // 更新GeoJSON数据源来反映地图上的选择变化
            updateOrdersData(orderStore.allOrders);
        } else {
            // 处理普通订单标记点击
            const { id } = feature.properties;
            console.log('点击了普通订单标记，ID:', id);

            // 获取订单对象
            const order = orderStore.allOrders.find(o => o.id === id);
            if (!order) {
                console.warn('Clicked order feature not found in store:', id);
                return;
            }

            // 检查订单状态，如果是未分配的closed或exceptional订单，则不允许选择
            if (!order.driver_id && (order.status === 'closed' || order.status === 'exceptional')) {
                console.log(`订单 ${order.id} 状态为 ${order.status}，不允许选择`);
                return;
            }

            // 检查是否按下了Ctrl键（多选模式）
            const isMultiSelect = e.originalEvent && (e.originalEvent.ctrlKey || e.originalEvent.metaKey);
            console.log('符号图层点击 - 检测Ctrl键:', isMultiSelect);

            // 处理订单选择
            if (isMultiSelect) {
                // Ctrl键多选模式：调用store action切换选中状态
                orderStore.toggleOrderSelection(id);
                console.log('多选模式：切换订单选中状态');
            } else {
                // 单选模式（与原始MapView.vue逻辑一致）
                const isCurrentlySelected = orderStore.selectedOrderIds.has(id);

                if (isCurrentlySelected) {
                    // 如果当前点击的订单已选中，则调用store action清空所有选择
                    orderStore.clearSelection();
                    console.log('单选模式：当前订单已选中，清空选择');
                } else {
                    // 如果当前点击的订单未选中，则调用store action清空所有选择，然后选中这一个
                    orderStore.clearSelection();
                    orderStore.toggleOrderSelection(id); // 使用 toggle 确保状态正确设置
                    console.log('单选模式：选中当前订单');
                }
            }

            // 更新GeoJSON数据源来反映地图上的选择变化
            updateOrdersData(orderStore.allOrders);
        }
    };

    // 处理标记鼠标进入事件 (再次重写逻辑)
    const handleSymbolMouseEnter = (e) => {
        if (!map.value || !e.features || e.features.length === 0) return;

        console.log('标记鼠标进入事件触发');

        const feature = e.features[0];
        console.log('悬停的特征:', feature);

        const featureId = feature.properties.id;
        const isCluster = feature.properties.isCluster;
        console.log('是否是聚合标记:', isCluster);

        // --- 强制重置 ---
        // 1. 清除任何正在进行的显示计时器
        if (popupHoverTimer.value) {
            clearTimeout(popupHoverTimer.value);
            popupHoverTimer.value = null;
        }
        // 2. 立即关闭任何已显示的弹窗
        hideOrderPopup();
        // 3. 更新当前悬停目标
        currentlyHoveredOrderId = featureId;
        // 4. 设置鼠标样式
        map.value.getCanvas().style.cursor = 'pointer';
        // --- 结束重置 ---

        // 捕获此计时器对应的目标ID
        const timeoutTargetId = featureId;

        // 设置新的延迟显示计时器
        popupHoverTimer.value = setTimeout(() => {
            // **最终检查**: 鼠标是否还在此标记上？
            if (currentlyHoveredOrderId !== timeoutTargetId) {
                console.log(`弹窗 ${timeoutTargetId} 显示中止，当前悬停于 ${currentlyHoveredOrderId}`);
                popupHoverTimer.value = null;
                return; // 不显示此弹窗
            }

            // --- 显示弹窗 ---
            console.log(`显示弹窗 for ${timeoutTargetId}`);

            let ordersToShow = [];
            let popupCoords;

            if (isCluster) {
                // 处理聚合标记悬停
                console.log('悬停在聚合标记上，包含订单数量:', feature.properties.clusterCount);

                // 获取聚合中的所有订单ID
                let clusterOrderIds = feature.properties.clusterOrderIds;

                // 检查是否是字符串，如果是，尝试解析为数组
                if (typeof clusterOrderIds === 'string') {
                    try {
                        clusterOrderIds = JSON.parse(clusterOrderIds);
                        console.log('解析后的聚合订单ID:', clusterOrderIds);
                    } catch (e) {
                        // 如果不是有效的JSON，尝试按逗号分割
                        clusterOrderIds = clusterOrderIds.split(',');
                        console.log('按逗号分割后的聚合订单ID:', clusterOrderIds);
                    }
                }

                if (!clusterOrderIds || !Array.isArray(clusterOrderIds) || clusterOrderIds.length === 0) {
                    console.warn('聚合标记缺少有效的订单ID列表');
                    popupHoverTimer.value = null;
                    return;
                }

                // 获取聚合中的所有订单
                ordersToShow = clusterOrderIds.map(id => orderStore.allOrders.find(o => o.id === id)).filter(o => o);
                console.log('聚合标记中的订单:', ordersToShow);

                if (ordersToShow.length === 0) {
                    console.warn('无法找到聚合标记中的订单');
                    popupHoverTimer.value = null;
                    return;
                }

                // 使用聚合标记的坐标
                const [lng, lat] = feature.geometry.coordinates;
                popupCoords = [lat, lng]; // 转换为 [lat, lng] 格式
                console.log('聚合标记坐标:', popupCoords);
            } else {
                // 处理普通订单标记悬停
                const order = orderStore.allOrders.find(o => o.id === timeoutTargetId);
                if (!order) {
                    popupHoverTimer.value = null;
                    return;
                }

                const coords = order.lng_lat || order.location;
                // 简化检查，假设 coords 总是有效
                if (!coords || !Array.isArray(coords) || coords.length !== 2) {
                    popupHoverTimer.value = null;
                    return;
                }

                popupCoords = coords;

                // 获取该位置的所有订单，显示所有订单，包括closed和exceptional状态的
                ordersToShow = findOrdersAtLocation(coords);
            }

            if (ordersToShow.length === 0) {
                popupHoverTimer.value = null;
                return;
            }

            const popupData = {
                orders: {
                    pickup: ordersToShow.filter(o => o.type && o.type.toUpperCase() === 'PICKUP'),
                    delivery: ordersToShow.filter(o => !o.type || o.type.toUpperCase() !== 'PICKUP')
                }
            };

            const popupElement = document.createElement('div');
            popupElement.className = 'marker-popup-container';
            // 重要：在 requestAnimationFrame 外创建 Vue App
            const popupApp = createApp({ render() { return h(MarkerPopup, { point: popupData }); } });
            popupApp.mount(popupElement);
            const anchor = calculateBestAnchor([popupCoords[1], popupCoords[0]]);
            const offset = getPopupOffset(anchor);

            // 创建新弹窗实例
            const newPopup = new maplibregl.Popup({
                closeButton: false,
                closeOnClick: false,
                anchor: anchor,
                offset: offset,
                maxWidth: '250px',
                className: 'marker-hover-popup'
            })
                .setLngLat([popupCoords[1], popupCoords[0]])
                .setDOMContent(popupElement);

            // 使用 requestAnimationFrame 延迟添加
            requestAnimationFrame(() => {
                 // 添加前再次检查 hover 状态!
                 if (currentlyHoveredOrderId !== timeoutTargetId) {
                      console.log(`弹窗 ${timeoutTargetId} 添加中止，悬停已改变。`);
                      try { popupApp.unmount(); } catch(err){} // 尝试清理Vue实例
                      popupHoverTimer.value = null;
                      return;
                 }
                 if (map.value) { // 再次检查 map 是否存在
                    newPopup.addTo(map.value);
                    activePopup.value = newPopup; // *在添加后*设置 ref

                    setTimeout(() => { ensurePopupInView(); }, 10);

                    const popupDomElement = activePopup.value?.getElement();
                    if (popupDomElement) {
                        popupDomElement.addEventListener('mouseenter', () => {
                            if (popupHoverTimer.value) {
                                clearTimeout(popupHoverTimer.value);
                                popupHoverTimer.value = null;
                            }
                        });
                        popupDomElement.addEventListener('mouseleave', () => {
                            hideOrderPopup();
                        });
                    }
                    lastPopupTime.value = Date.now();
                 } else {
                     console.warn("Map 不存在，无法添加弹窗");
                     try { popupApp.unmount(); } catch(err){} // 清理Vue实例
                 }
                  popupHoverTimer.value = null; // 计时器完成
            });
             // --- 结束显示 ---

        }, POPUP_HOVER_DELAY);
    };

    // 处理标记鼠标离开事件 (再次重写逻辑 V3)
    const handleSymbolMouseLeave = () => {
        // 1. 重置当前悬停目标ID
        const previouslyHoveredId = currentlyHoveredOrderId;
        currentlyHoveredOrderId = null;

        // 2. 清除任何"计划显示"的计时器
        if (popupHoverTimer.value) {
            clearTimeout(popupHoverTimer.value);
            popupHoverTimer.value = null;
        }

        // 3. 检查是否需要立即关闭已显示的弹窗
        const popupToCheck = activePopup.value; // 获取当前弹窗引用
        if (popupToCheck) {
            // 使用 requestAnimationFrame 延迟到下一帧检查
            requestAnimationFrame(() => {
                // 再次检查弹窗实例是否还存在 (可能已被 mouseenter 关闭)
                // 并且检查当前是否没有新的悬停目标 (确认鼠标是移出，而不是移入新标记)
                if (activePopup.value === popupToCheck && currentlyHoveredOrderId === null) {
                    const popupElement = popupToCheck.getElement();
                    // 检查弹窗元素是否存在且鼠标不在其上
                    if (popupElement && !popupElement.matches(':hover')) {
                        console.log(`Mouse left marker ${previouslyHoveredId} and not on popup, closing.`);
                        hideOrderPopup();
                    } else {
                        // console.log(`Mouse left marker ${previouslyHoveredId} but moved onto popup.`);
                    }
                } else {
                   // console.log(`Popup ${previouslyHoveredId} likely already closed or new hover started.`);
                }
            });
        }

        // 4. 重置鼠标样式 (如果确实离开了交互区域)
        requestAnimationFrame(() => { // 也延迟一下
             if (!currentlyHoveredOrderId && map.value) {
                 const popupElement = activePopup.value ? activePopup.value.getElement() : null;
                 if (!popupElement || !popupElement.matches(':hover')) {
                       map.value.getCanvas().style.cursor = '';
                 }
            }
        });
    };

    // 清除订单符号图层的数据和自定义图标
    const clearOrderSymbols = () => {
        if (!map.value) return;

        const source = map.value.getSource('orders-source');
        if (source) {
            source.setData({
                type: 'FeatureCollection',
                features: []
            });
            console.log('Cleared orders-source GeoJSON data.');
        }

        // 同时清除生成的自定义图标
        clearCustomIcons();
    };

    // 更新订单标记
    const updateMarkers = (orders) => {
        if (!map.value || isUpdatingMarkers.value) return;

        isUpdatingMarkers.value = true;

        try {
            console.log('更新订单标记，数量:', orders.length);

            // 强制清除订单缓存，确保使用最新状态
            if (typeof orderStore.invalidateCache === 'function') {
                orderStore.invalidateCache();
            }

            // 获取最新的全局订单数据，确保状态是最新的
            const allOrders = orderStore.allOrders || [];

            // 确保orders中的每个订单都是最新状态
            const updatedOrders = orders.map(order => {
                // 尝试从全局订单中找到对应订单，确保获取最新状态
                const latestOrder = allOrders.find(o => o.id === order.id);
                return latestOrder || order;
            });

            // 调用清除自定义图标，确保使用最新的图标
            clearCustomIcons();

            // 更新源数据
            updateOrdersData(updatedOrders);

            // 如果当前有选中的订单，确保它们在最上层
            if (orderStore.selectedOrderIds && orderStore.selectedOrderIds.size > 0) {
                moveSymbolLayersToTop();
            }

            // 记录更新时间
            lastUpdateTime.value = Date.now();

            console.log('标记更新完成，总数:', updatedOrders.length);
        } catch (error) {
            console.error('更新标记时出错:', error);
        } finally {
            // 设置延迟，防止短时间内多次更新
            setTimeout(() => {
                isUpdatingMarkers.value = false;
            }, 100);
        }
    };

    // 处理订单更新事件
    const handleOrdersUpdated = (event) => {
        if (!map.value) return;

        console.log('订单更新事件:', event);

        // 优先处理拖拽操作导致的更新
        if (event.isDragOperation && event.orders && Array.isArray(event.orders)) {
            console.log('检测到拖拽操作，优先更新停靠点编号');

            try {
                // 立即更新这些订单的标记
                if (event.orders.length > 0) {
                    console.log(`更新${event.orders.length}个拖拽操作的订单标记`);

                    // 强制清除图标缓存并重新创建，确保停靠点编号正确显示
                    clearCustomIcons();
                    addMarkerImages();

                    // 直接使用事件中的订单数据更新地图
                    // 这样可以确保立即反映最新的停靠点编号
                    updateOrdersData(event.orders);

                    // 单独更新每个订单的标记，确保停靠点编号正确显示
                    event.orders.forEach(order => {
                        // 使用updateMarker函数更新单个标记
                        updateMarker(order);
                    });

                    // 如果是拖拽过程中的更新，不需要清除图标和重新创建
                    if (!event.isDragging) {
                        // 拖拽结束后，确保图标显示正确
                        moveSymbolLayersToTop();
                    }

                    // 如果当前有选中的路线，重新绘制路线
                    const routeStore = useRouteStore();
                    if (routeStore.selectedRoute && event.routeId === routeStore.selectedRoute.id) {
                        // 通知其他组件重新绘制路线
                        eventBus.emit(EVENT_TYPES.ROUTE_UPDATED, {
                            routeId: event.routeId,
                            orders: event.orders,
                            forceRedraw: true
                        });
                    }
                }

                return; // 处理完毕，提前返回
            } catch (error) {
                console.error('更新拖拽操作订单标记时出错:', error);
                // 发生错误时继续执行下面的全量更新逻辑
            }
        }

        // 如果是状态变更事件，只更新变化的订单标记
        if (event.isStatusChange && event.orders && Array.isArray(event.orders)) {
            console.log('检测到订单状态变更事件，只更新变化的订单标记');

            try {
                // 获取变化订单的ID列表
                const changedOrderIds = event.orders.map(order => order.id);

                // 仅更新这些变化的订单
                if (changedOrderIds.length > 0) {
                    console.log(`更新${changedOrderIds.length}个状态变更的订单标记`);

                    // 从当前显示订单中过滤出需要更新的订单
                    const currentOrdersToDisplay = window.filteredOrdersFromList ||
                                                  (window.showAllOrders ? orderStore.allOrders : orderStore.orders);

                    // 过滤出需要更新的订单
                    const ordersToUpdate = currentOrdersToDisplay.filter(
                        order => changedOrderIds.includes(order.id)
                    );

                    if (ordersToUpdate.length > 0) {
                        // 如果找到了对应的订单，只更新这些订单
                        console.log(`找到${ordersToUpdate.length}个需要更新标记的订单`);
                        updateOrdersData(ordersToUpdate);
                    } else {
                        console.log('在当前视图中未找到对应的订单，跳过更新');
                    }
                }

                return; // 处理完毕，提前返回
            } catch (error) {
                console.error('更新订单状态标记时出错:', error);
                // 发生错误时继续执行下面的全量更新逻辑
            }
        }

        // 如果是分配或取消分配操作，立即更新地图显示
        if (event.isAssign || event.isUnassign) {
            console.log('检测到分配/取消分配操作，立即更新地图标记');

            // 获取当前显示的订单
            const currentOrdersToDisplay = window.filteredOrdersFromList ||
                                         (window.showAllOrders ? orderStore.allOrders : orderStore.orders);

            // 强制清除图标并重新创建
            clearCustomIcons();
            addMarkerImages();

            // 更新标记
            updateMarkers(currentOrdersToDisplay);
        }
    };

    // 处理拖拽开始事件
    const handleDragStart = () => {
        console.log('MarkerManagement 收到拖拽开始事件，关闭弹窗');
        hideOrderPopup();
    };

    // 监听拖拽开始事件
    eventBus.on(EVENT_TYPES.DRAG_START, handleDragStart);















    // 添加单个订单标记
    const addMarker = (order) => {
        console.log(`添加单个订单标记: ${order.id}`);

        if (!map.value || !map.value.getSource('orders-source')) {
            console.error('地图或数据源不存在，无法添加标记');
            return;
        }

        try {
            // 获取当前数据源中的所有特征
            const source = map.value.getSource('orders-source');
            const currentData = source._data || { type: 'FeatureCollection', features: [] };

            // 检查订单是否已存在
            const existingFeatureIndex = currentData.features.findIndex(f => f.properties.id === order.id);
            if (existingFeatureIndex !== -1) {
                console.log(`订单 ${order.id} 已存在，更新现有标记`);
                updateMarker(order, true); // 强制更新现有标记
                return;
            }

            // 获取坐标
            const coords = order.lng_lat || order.location;
            if (!coords || !Array.isArray(coords) || coords.length !== 2) {
                console.error(`订单 ${order.id} 缺少有效坐标，无法添加标记`);
                return;
            }

            // 注意：我们的坐标是 [lat, lng] 格式，而 GeoJSON 需要 [lng, lat] 格式
            const [lat, lng] = coords;
            if (isNaN(lat) || isNaN(lng)) {
                console.error(`订单 ${order.id} 坐标格式无效: [${lat}, ${lng}]`);
                return;
            }

            // 检查订单是否已分配
            const isAssigned = !!order.driver_id;

            // 获取停靠点编号
            let stopNo = '';
            if (isAssigned) {
                // 优先使用stop_no属性
                if (order.stop_no !== undefined && order.stop_no !== null) {
                    stopNo = String(order.stop_no);
                }
                // 备选：使用pickup_stop_no属性
                else if (order.pickup_stop_no) {
                    stopNo = String(order.pickup_stop_no);
                }
                // 如果两个都没有但有订单号，显示订单号最后一位
                else if (order.no) {
                    const orderNo = String(order.no);
                    stopNo = orderNo.slice(-1);
                }
            } else {
                // 未分配订单显示订单号的最后一位或索引
                if (order.no) {
                    const orderNo = String(order.no);
                    stopNo = orderNo.slice(-1);
                } else {
                    // 最后的备选是使用1
                    stopNo = '1';
                }
            }

            // 获取司机颜色
            const driverColor = isAssigned ? getMarkerColor(order) : '#9E9E9E';

            // 检查是否为选中的订单
            const isSelected = orderStore.selectedOrderIds.has(order.id);

            // 构建基于订单ID和停靠点编号的唯一图标ID
            const uniqIconSuffix = `${order.id}-${stopNo || '0'}`;

            // 为每个订单创建特定的图标ID
            let iconId;
            if (isSelected) {
                // 选中状态的图标ID
                iconId = isAssigned
                    ? `selected-assigned-${uniqIconSuffix}`
                    : `selected-unassigned-${uniqIconSuffix}`;
            } else {
                // 非选中状态的图标ID
                iconId = isAssigned
                    ? `assigned-${uniqIconSuffix}`
                    : `unassigned-${uniqIconSuffix}`;
            }

            // 创建图标
            createSVGMarkerImage(
                iconId,
                isAssigned ? driverColor : '#9E9E9E',
                isAssigned,
                isSelected,
                stopNo,
                order.status,
                order.type && order.type.toUpperCase()
            );

            // 将图标ID添加到集合
            createdIcons.value.add(iconId);

            // 创建新的GeoJSON特征对象
            const newFeature = {
                type: 'Feature',
                geometry: {
                    type: 'Point',
                    coordinates: [lng, lat]
                },
                properties: {
                    id: order.id,
                    status: isAssigned ? 'assigned' : 'unassigned',
                    stopNo: stopNo,
                    driverId: order.driver_id || null,
                    routeId: order.route_id || null,
                    selected: isSelected,
                    iconImage: iconId,
                    color: driverColor,
                    zIndexOffset: isSelected ? 1000 : (isAssigned ? 0 : 500) // 优先显示未分配订单：选中(1000) > 未分配(500) > 已分配(0)
                }
            };

            // 添加新特征
            currentData.features.push(newFeature);

            // 更新数据源
            source.setData(currentData);

            // 确保标记图层在最顶部
            moveSymbolLayersToTop();

            console.log(`成功添加订单标记: ${order.id}`);

            return true;
        } catch (error) {
            console.error(`添加订单标记时出错: ${error.message}`);
            return false;
        }
    };

    return {
        markers,
        isUpdatingMarkers,
        popupCache,
        activePopup,
        performanceStats,
        selectedOrders,
        createCustomMarkerElement,
        addMarkerLayers,
        addMarkerImages,
        updateMarkers,
        showOrderPopup,
        hideOrderPopup,

        // 辅助函数
        findOrdersAtLocation,
        getMarkerColor,
        updateOrdersData,
        updateSingleOrderMarker, // 添加单个订单标记更新函数
        clearCustomIcons,
        updateMarkerSizeForZoom,
        moveSymbolLayersToTop,

        // 新增导出
        clearOrderSymbols,
        handleOrdersUpdated,
        updateMarker,
        addMarker // 导出新添加的函数
    };
}