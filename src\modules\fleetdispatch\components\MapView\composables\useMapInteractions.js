import { ref, reactive } from 'vue';
import { useOrderStore } from '../../../stores/order';
import { eventBus, EVENT_TYPES } from '../../../utils/eventBus';
import maplibregl from 'maplibre-gl';

export function useMapInteractions(map, mapContainer) {
    const orderStore = useOrderStore();

    // 地图交互状态
    const isHandlingMapEvent = ref(false);
    const isHandlingListEvent = ref(false);
    const lastClickTime = ref(0);
    const clickDebounceTime = 300;

    // 选择框相关
    const isDrawingSelectionBox = ref(false);
    const selectionBox = ref(null);
    const selectionStart = reactive({ x: 0, y: 0 });
    const selectionCurrent = reactive({ x: 0, y: 0 });

    // 键盘状态
    const isCtrlPressed = ref(false);
    const isShiftPressed = ref(false);

    // 选择提示相关
    const showSelectionTooltip = ref(false);
    const selectionTooltipContent = ref('按住Ctrl键并拖动鼠标可批量选择或反选订单');

    // 保存选中的订单集合
    const selectedOrders = ref(new Set());

    // 绑定地图事件处理
    const bindMapEventHandlers = () => {
        if (!map.value || !mapContainer.value) return;

        console.log('绑定地图事件处理器');

        // 绑定地图点击事件
        map.value.on('click', handleMapClick);

        // 绑定鼠标事件以支持矩形选择
        mapContainer.value.addEventListener('mousedown', handleMouseDown);
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);

        // 绑定键盘事件
        document.addEventListener('keydown', handleKeyDown);
        document.addEventListener('keyup', handleKeyUp);

        // 添加鼠标离开地图容器的事件处理
        mapContainer.value.addEventListener('mouseleave', handleMapMouseLeave);

        console.log('地图事件处理器绑定完成');
    };

    // 解绑地图事件处理
    const unbindMapEventHandlers = () => {
        if (!map.value) return;

        // 解绑地图点击事件
        map.value.off('click', handleMapClick);

        // 解绑鼠标事件
        if (mapContainer.value) {
            mapContainer.value.removeEventListener('mousedown', handleMouseDown);
            mapContainer.value.removeEventListener('mouseleave', handleMapMouseLeave);
        }

        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);

        // 解绑键盘事件
        document.removeEventListener('keydown', handleKeyDown);
        document.removeEventListener('keyup', handleKeyUp);
    };

    // 处理地图点击事件
    const handleMapClick = e => {
        // 如果事件已被阻止或正在绘制选择框，不处理
        if (e.defaultPrevented || isDrawingSelectionBox.value) return

        // 矩形选择完成后的短时间内不清除选择（防止误触）
        const now = Date.now()
        if (now - lastClickTime.value < clickDebounceTime) {
            console.log('点击太快，忽略清除选择')
            return
        }
        lastClickTime.value = now

        // 检查是否按住了Ctrl键，如果按住则不清除选择
        if (isCtrlPressed.value) {
            console.log('按住Ctrl键点击，不清除选择')
            return
        }

        // 检查点击位置是否有标记
        const features = map.value.queryRenderedFeatures(e.point, {
            layers: ['unassigned-orders-layer', 'assigned-orders-layer', 'selected-orders-layer']
        })

        // 如果点击位置有标记，不清除选择（让标记的点击事件处理）
        if (features.length > 0) {
            return
        }

        console.log('地图空白处点击 - 清除订单选择')

        // 防止处理中断
        if (isHandlingMapEvent.value) return

        try {
            isHandlingMapEvent.value = true

            // 清除订单选择
            orderStore.clearSelection()

            // 向事件总线发送清除选择事件
            eventBus.emit(EVENT_TYPES.ORDERS_SELECTION_CLEARED, {
                source: 'map'
            })
        } finally {
            setTimeout(() => {
                isHandlingMapEvent.value = false
            }, 100)
        }
    };

    // 处理键盘按下事件
    const handleKeyDown = e => {
        if (e.key === 'Control' || e.key === 'Meta') {
            isCtrlPressed.value = true;
            selectionTooltipContent.value = '拖动鼠标框选区域可选择订单，重复框选相同区域会反选订单';
            showSelectionTooltip.value = true;
        } else if (e.key === 'Shift') {
            isShiftPressed.value = true;
            selectionTooltipContent.value = '按住Shift键可执行其他操作';
            showSelectionTooltip.value = true;
        }
    };

    // 处理键盘释放事件
    const handleKeyUp = e => {
        if (e.key === 'Control' || e.key === 'Meta') {
            isCtrlPressed.value = false;
            selectionTooltipContent.value = '按住Ctrl键并拖动鼠标可批量选择或反选订单';
            showSelectionTooltip.value = true;
            setTimeout(() => {
                showSelectionTooltip.value = false;
            }, 2000);
        } else if (e.key === 'Shift') {
            isShiftPressed.value = false;
        }
    };

    // 处理鼠标按下事件
    const handleMouseDown = e => {
        // 只有当按住Ctrl键且点击的是地图容器本身时才启动矩形选择
        if (isCtrlPressed.value && e.target === map.value?.getCanvas()) {
            e.preventDefault();
            e.stopPropagation();

            // 记录起始点
            const mapCanvas = map.value.getCanvasContainer();
            const rect = mapCanvas.getBoundingClientRect();
            selectionStart.x = e.clientX - rect.left;
            selectionStart.y = e.clientY - rect.top;
            selectionCurrent.x = selectionStart.x;
            selectionCurrent.y = selectionStart.y;

            // 显示选择框并设置状态
            isDrawingSelectionBox.value = true;
            updateSelectionBox();

            // 隐藏提示
            showSelectionTooltip.value = false;

            // 确保 mousemove 和 mouseup 监听器已添加到 document (如果尚未添加)
            // 注意：这里我们假设 bindMapEventHandlers 已经添加了这些监听器
            // 如果 bindMapEventHandlers 没有添加，则需要在此处添加
        }
    };

    // 处理鼠标移动 - 更新选择框
    const handleMouseMove = e => {
        if (!isDrawingSelectionBox.value) return;

        const mapContainer = map.value.getCanvasContainer();
        const rect = mapContainer.getBoundingClientRect();
        selectionCurrent.x = e.clientX - rect.left;
        selectionCurrent.y = e.clientY - rect.top;

        updateSelectionBox();
    };

    // 处理鼠标抬起 - 完成选择
    const handleMouseUp = e => {
        if (!isDrawingSelectionBox.value) return;

        // 阻止事件冒泡，防止触发地图的点击事件 (如果点击发生在Canvas上)
        if (e.target === map.value?.getCanvas()) {
             e.preventDefault();
             e.stopPropagation();
        }

        // 获取选择区域内的订单
        selectOrdersInBox();

        // 隐藏选择框并重置状态
        isDrawingSelectionBox.value = false;

        // 设置一个标志，防止接下来的地图点击事件清除选择
        lastClickTime.value = Date.now();
    };

    // 处理鼠标离开地图
    const handleMapMouseLeave = () => {
        // 隐藏提示
        showSelectionTooltip.value = false;
    };

    // 更新选择框位置和大小
    const updateSelectionBox = () => {
        if (!selectionBox.value) return;

        const left = Math.min(selectionStart.x, selectionCurrent.x);
        const top = Math.min(selectionStart.y, selectionCurrent.y);
        const width = Math.abs(selectionStart.x - selectionCurrent.x);
        const height = Math.abs(selectionStart.y - selectionCurrent.y);

        selectionBox.value.style.left = `${left}px`;
        selectionBox.value.style.top = `${top}px`;
        selectionBox.value.style.width = `${width}px`;
        selectionBox.value.style.height = `${height}px`;
    };

    // 选择框内的订单
    const selectOrdersInBox = () => {
        if (!map.value) return

        // 获取选择框大小，如果太小，则可能是意外点击，不执行选择
        const width = Math.abs(selectionStart.x - selectionCurrent.x)
        const height = Math.abs(selectionStart.y - selectionCurrent.y)
        if (width < 5 || height < 5) {
            return
        }

        // 计算选择框的地理范围
        const bounds = new maplibregl.LngLatBounds()

        // 添加各个角落的点
        bounds.extend(map.value.unproject([
            Math.min(selectionStart.x, selectionCurrent.x),
            Math.min(selectionStart.y, selectionCurrent.y)
        ]))

        bounds.extend(map.value.unproject([
            Math.max(selectionStart.x, selectionCurrent.x),
            Math.min(selectionStart.y, selectionCurrent.y)
        ]))

        bounds.extend(map.value.unproject([
            Math.min(selectionStart.x, selectionCurrent.x),
            Math.max(selectionStart.y, selectionCurrent.y)
        ]))

        bounds.extend(map.value.unproject([
            Math.max(selectionStart.x, selectionCurrent.x),
            Math.max(selectionStart.y, selectionCurrent.y)
        ]))

        console.log('框选区域经纬度范围:', bounds.toString())

        // 找出边界框内的所有订单，排除未分配的closed、exceptional和已取货的取货订单
        const currentOrders = window.filteredOrdersFromList || orderStore.allOrders
        const ordersInBox = currentOrders.filter(order => {
            // 排除未分配的closed、exceptional和已取货的取货订单
            if (!order.driver_id && (order.status === 'closed' || order.status === 'exceptional' ||
                                   (order.type === 'PICKUP' && order.status === 'pickedUp'))) {
                return false;
            }

            const coords = order.lng_lat || order.location
            if (!coords || !Array.isArray(coords) || coords.length !== 2) return false

            // 注意：我们的坐标是 [lat, lng] 格式，而 MapLibre 需要 [lng, lat] 格式
            const [lat, lng] = coords
            const isInBounds = bounds.contains([lng, lat])

            if (isInBounds) {
                console.log(`订单 ${order.id} 在框选范围内, 坐标: [${lng}, ${lat}]`)
            }

            return isInBounds
        })

        console.log('选择框内的订单数量:', ordersInBox.length, '当前订单总数:', currentOrders.length)

        // 如果有订单，则处理选择
        if (ordersInBox.length > 0) {
            console.log('框选区域内的订单IDs:', ordersInBox.map(o => o.id))

            // 获取当前已选中的订单ID集合
            const currentSelectedIds = new Set(orderStore.selectedOrderIds)
            const newSelectedIds = new Set(currentSelectedIds) // 创建副本用于修改

            // 实现Windows风格的交互：
            // 1. 如果订单之前未选中，则选中它
            // 2. 如果订单之前已选中，则取消选中它
            ordersInBox.forEach(order => {
                if (currentSelectedIds.has(order.id)) {
                    // 如果订单之前已选中，从新集合中移除（取消选中）
                    newSelectedIds.delete(order.id)
                    console.log(`取消选中已选订单: ${order.id}`)
                } else {
                    // 如果订单之前未选中，添加到新集合（选中）
                    newSelectedIds.add(order.id)
                    console.log(`选中新订单: ${order.id}`)
                }
            })

            // 将新的选择集合应用到store
            const orderIdsArray = Array.from(newSelectedIds)
            console.log('更新后的所有选中订单IDs:', orderIdsArray, '共', orderIdsArray.length, '个')

            // 计算新选中和取消选中的数量
            const newlySelected = ordersInBox.filter(o => !currentSelectedIds.has(o.id)).length
            const newlyUnselected = ordersInBox.filter(o => currentSelectedIds.has(o.id)).length

            // 防止事件处理中断选择状态
            isHandlingListEvent.value = true

            try {
                // 使用selectOrders方法确保订单的isSelected属性被正确更新
                orderStore.selectOrders(orderIdsArray)

                // 触发批量选择事件，通知其他组件（如订单列表）更新
                eventBus.emit(EVENT_TYPES.ORDERS_BATCH_SELECTED, {
                    orderIds: orderIdsArray,
                    source: 'map'
                })

                // 显示选择成功提示
                let tooltipMessage = ''
                if (newlySelected > 0 && newlyUnselected > 0) {
                    tooltipMessage = `选中 ${newlySelected} 个订单，取消选中 ${newlyUnselected} 个订单，总计选中 ${orderIdsArray.length} 个`
                } else if (newlySelected > 0) {
                    tooltipMessage = `选中 ${newlySelected} 个新订单，总计选中 ${orderIdsArray.length} 个订单`
                } else if (newlyUnselected > 0) {
                    tooltipMessage = `取消选中 ${newlyUnselected} 个订单，总计选中 ${orderIdsArray.length} 个订单`
                }

                selectionTooltipContent.value = tooltipMessage
                showSelectionTooltip.value = true

                // 定时隐藏提示
                setTimeout(() => {
                    showSelectionTooltip.value = false
                }, 2000)
            } finally {
                // 确保事件处理标志最终被重置
                setTimeout(() => {
                    isHandlingListEvent.value = false
                }, 200)
            }
        }
    };

    return {
        isHandlingMapEvent,
        isHandlingListEvent,
        lastClickTime,
        clickDebounceTime,
        isDrawingSelectionBox,
        selectionBox,
        selectionStart,
        selectionCurrent,
        isCtrlPressed,
        isShiftPressed,
        showSelectionTooltip,
        selectionTooltipContent,
        selectedOrders,
        bindMapEventHandlers,
        unbindMapEventHandlers,
        handleMapClick,
        selectOrdersInBox,
        updateSelectionBox
    };
}