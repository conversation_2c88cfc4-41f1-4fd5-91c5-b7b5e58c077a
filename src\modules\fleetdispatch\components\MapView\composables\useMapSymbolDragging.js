// src/modules/fleetdispatch/components/MapView/composables/useMapSymbolDragging.js
import { ref, computed } from 'vue'
import { useOrderStore } from '../../../stores/order'
import { eventBus, EVENT_TYPES } from '../../../utils/eventBus'
import { throttle } from 'lodash'

export function useMapSymbolDragging(map, moveSymbolLayersToTop) {
    const orderStore = useOrderStore()

    // 拖拽状态管理
    const isDraggingMapSymbol = ref(false)
    const draggedOrderInfo = ref(null) // { orderId, routeId, originalLngLat, originalScreenPoint }
    const affectedRouteId = ref(null)
    const isMultiDragMap = ref(false)
    const multiDragMapOrderIds = ref([])

    // 新增：拖拽类型管理
    const dragType = ref(null) // 'assigned' | 'unassigned'
    const isUnassignedDrag = ref(false)

    // 拖拽视觉反馈
    const ghostMarker = ref(null)
    const highlightedTargetId = ref(null)

    // 初始化拖拽事件处理
    const initDragHandlers = () => {
        if (!map.value) return

        // 为订单 Symbol 图层添加鼠标事件
        const orderLayers = ['unassigned-orders-layer', 'assigned-orders-layer', 'selected-orders-layer']

        // 先移除所有现有的事件监听，防止重复绑定
        orderLayers.forEach(layerId => {
            map.value.off('mousedown', layerId, handleSymbolMouseDown)
            map.value.off('touchstart', layerId, handleSymbolTouchStart)
        })

        document.removeEventListener('mousemove', handleSymbolDrag)
        document.removeEventListener('mouseup', handleSymbolMouseUp)
        document.removeEventListener('touchmove', handleSymbolTouchMove)
        document.removeEventListener('touchend', handleSymbolTouchEnd)

        // 添加新的事件监听
        orderLayers.forEach(layerId => {
            // 鼠标按下事件 - 开始拖拽
            map.value.on('mousedown', layerId, handleSymbolMouseDown)
        })

        // 全局鼠标事件 - 处理拖拽过程和结束
        document.addEventListener('mousemove', handleSymbolDrag)
        document.addEventListener('mouseup', handleSymbolMouseUp)

        // 触摸事件支持 - 可选
        orderLayers.forEach(layerId => {
            map.value.on('touchstart', layerId, handleSymbolTouchStart)
        })
        document.addEventListener('touchmove', handleSymbolTouchMove)
        document.addEventListener('touchend', handleSymbolTouchEnd)

        console.log('Symbol 图层拖拽事件处理器已初始化')
    }

    // 清理拖拽事件处理
    const cleanupDragHandlers = () => {
        if (!map.value) return

        const orderLayers = ['unassigned-orders-layer', 'assigned-orders-layer', 'selected-orders-layer']

        orderLayers.forEach(layerId => {
            map.value.off('mousedown', layerId, handleSymbolMouseDown)
            map.value.off('touchstart', layerId, handleSymbolTouchStart)
        })

        document.removeEventListener('mousemove', handleSymbolDrag)
        document.removeEventListener('mouseup', handleSymbolMouseUp)
        document.removeEventListener('touchmove', handleSymbolTouchMove)
        document.removeEventListener('touchend', handleSymbolTouchEnd)

        console.log('Symbol 图层拖拽事件处理器已清理')
    }

    // 处理鼠标按下事件
    const handleSymbolMouseDown = (e) => {
        // MapLibre GL JS 事件对象可能没有标准的 preventDefault 和 stopPropagation 方法
        // 我们可以使用 originalEvent 属性来访问原始的 DOM 事件
        if (e.originalEvent) {
            e.originalEvent.preventDefault()
            e.originalEvent.stopPropagation()
        }

        // 获取点击的订单特征
        const feature = e.features[0]
        if (!feature || !feature.properties || !feature.properties.id) return

        const featureId = feature.properties.id
        const isCluster = feature.properties.isCluster

        // 处理聚合标记
        if (isCluster) {
            console.log('拖拽聚合标记，包含订单数量:', feature.properties.clusterCount)

            // 获取聚合中的所有订单ID
            let clusterOrderIds = feature.properties.clusterOrderIds
            console.log('原始聚合标记中的订单ID:', clusterOrderIds, '类型:', typeof clusterOrderIds)

            // 检查是否是字符串，如果是，尝试解析为数组
            if (typeof clusterOrderIds === 'string') {
                try {
                    clusterOrderIds = JSON.parse(clusterOrderIds)
                    console.log('解析后的聚合订单ID:', clusterOrderIds)
                } catch (e) {
                    // 如果不是有效的JSON，尝试按逗号分割
                    clusterOrderIds = clusterOrderIds.split(',')
                    console.log('按逗号分割后的聚合订单ID:', clusterOrderIds)
                }
            }

            if (!clusterOrderIds || !Array.isArray(clusterOrderIds) || clusterOrderIds.length === 0) {
                console.warn('聚合标记缺少有效的订单ID列表')
                return
            }

            // 分析聚合中的订单状态
            const clusterAnalysis = handleMixedClusterDrag(clusterOrderIds)

            // 如果是混合状态，自动选择未分配订单
            if (clusterAnalysis.type === 'mixed') {
                console.log('检测到混合状态聚合标记，自动选择未分配订单进行拖拽')
                console.log(`混合状态分析: 已分配${clusterAnalysis.assignedOrders.length}单, 未分配${clusterAnalysis.unassignedOrders.length}单`)

                // 自动选择未分配订单
                const selection = {
                    type: 'unassigned_only',
                    orders: clusterAnalysis.unassignedOrders
                }

                console.log(`自动选择拖拽未分配订单: ${selection.orders.length}单`)

                // 根据选择开始拖拽
                startClusterDragWithSelection(selection, e)

                return // 开始拖拽，不继续执行
            }

            // 非混合状态，使用原有逻辑
            const ordersToMove = clusterAnalysis.orders
            const firstOrder = ordersToMove[0]

            // 设置拖拽状态
            isDraggingMapSymbol.value = true

            // 判断拖拽类型
            if (firstOrder.route_id) {
                dragType.value = 'assigned'
                isUnassignedDrag.value = false
                affectedRouteId.value = firstOrder.route_id
                console.log('开始拖拽已分配订单聚合')
            } else {
                dragType.value = 'unassigned'
                isUnassignedDrag.value = true
                affectedRouteId.value = null
                console.log('开始拖拽未分配订单聚合')
            }

            // 禁用地图拖拽功能
            if (map.value) {
                map.value.dragPan.disable()
            }

            // 发送事件通知其他组件拖拽开始
            eventBus.emit(EVENT_TYPES.DRAG_START, {
                orderId: firstOrder.id,
                routeId: firstOrder.route_id,
                timestamp: Date.now()
            })

            // 记录原始位置信息
            const lngLat = e.lngLat
            const screenPoint = map.value.project(lngLat)

            draggedOrderInfo.value = {
                orderId: firstOrder.id,
                routeId: firstOrder.route_id,
                originalLngLat: [lngLat.lng, lngLat.lat],
                originalScreenPoint: [screenPoint.x, screenPoint.y]
            }

            // 使用分析后的订单列表
            if (ordersToMove.length > 1) {
                isMultiDragMap.value = true
                multiDragMapOrderIds.value = ordersToMove.map(o => o.id)
                console.log(`聚合标记拖拽 (${dragType.value}): ${multiDragMapOrderIds.value.length} 个订单`)
            }

            // 创建拖拽视觉反馈 (幽灵标记)
            createGhostMarker(lngLat, firstOrder, feature.properties.clusterCount)

            // 高亮被拖拽的订单标记
            highlightDraggedSymbols()

            return
        }

        // 处理普通订单标记
        const orderId = featureId
        // 使用 orderStore.allOrders.find 代替不存在的 getOrderById 方法
        const order = orderStore.allOrders.find(o => o.id === orderId)

        // 检查订单是否存在
        if (!order) {
            console.log('订单不存在')
            return
        }

        // 设置拖拽状态
        isDraggingMapSymbol.value = true

        // 判断拖拽类型
        if (order.route_id) {
            dragType.value = 'assigned'
            isUnassignedDrag.value = false
            affectedRouteId.value = order.route_id
            console.log(`开始拖拽已分配订单: ${orderId}`)
        } else {
            dragType.value = 'unassigned'
            isUnassignedDrag.value = true
            affectedRouteId.value = null
            console.log(`开始拖拽未分配订单: ${orderId}`)
        }

        // 禁用地图拖拽功能
        if (map.value) {
            map.value.dragPan.disable()
        }

        // 发送事件通知其他组件拖拽开始
        eventBus.emit(EVENT_TYPES.DRAG_START, {
            orderId: order.id,
            routeId: order.route_id,
            timestamp: Date.now()
        })

        // 记录原始位置信息
        const lngLat = e.lngLat
        const screenPoint = map.value.project(lngLat)

        draggedOrderInfo.value = {
            orderId,
            routeId: order.route_id,
            originalLngLat: [lngLat.lng, lngLat.lat],
            originalScreenPoint: [screenPoint.x, screenPoint.y]
        }

        // 检查是否有多选
        let useMultiSelect = false;
        if (orderStore.selectedOrderIds.has(orderId) && orderStore.selectedOrderIds.size > 1) {
            // 验证所有选中的订单是否属于同一路线
            const selectedOrders = Array.from(orderStore.selectedOrderIds)
                .map(id => orderStore.allOrders.find(o => o.id === id))
                .filter(o => o && o.route_id === affectedRouteId.value)

            if (selectedOrders.length > 1) {
                isMultiDragMap.value = true
                multiDragMapOrderIds.value = selectedOrders.map(o => o.id)
                console.log(`多选拖拽: ${multiDragMapOrderIds.value.length} 个订单`)
                useMultiSelect = true;
            }
        }

        // 如果没有多选，检查同一位置是否有多个订单
        if (!useMultiSelect) {
            if (dragType.value === 'assigned') {
                // 已分配订单：获取当前路线的所有订单
                const routeOrders = orderStore.getOrdersByRouteNumber(affectedRouteId.value);

                // 查找与当前订单位置相同的其他订单
                const sameLocationOrders = routeOrders.filter(o => {
                    if (o.id === orderId) return false; // 排除当前订单

                    // 检查位置是否相同
                    const orderLocation = o.lng_lat || o.location;
                    const currentLocation = order.lng_lat || order.location;

                    return orderLocation === currentLocation;
                });

                // 如果找到同一位置的其他订单，将它们添加到拖拽列表中
                if (sameLocationOrders.length > 0) {
                    isMultiDragMap.value = true;
                    multiDragMapOrderIds.value = [orderId, ...sameLocationOrders.map(o => o.id)];
                    console.log(`同一位置多订单拖拽 (已分配): ${multiDragMapOrderIds.value.length} 个订单`);
                }
            } else {
                // 未分配订单：查找同一位置的其他未分配订单
                const allUnassignedOrders = orderStore.allOrders.filter(o => !o.route_id);

                // 查找与当前订单位置相同的其他未分配订单
                const sameLocationOrders = allUnassignedOrders.filter(o => {
                    if (o.id === orderId) return false; // 排除当前订单

                    // 检查位置是否相同
                    const orderLocation = o.lng_lat || o.location;
                    const currentLocation = order.lng_lat || order.location;

                    return orderLocation && currentLocation &&
                           orderLocation[0] === currentLocation[0] &&
                           orderLocation[1] === currentLocation[1];
                });

                // 如果找到同一位置的其他未分配订单，将它们添加到拖拽列表中
                if (sameLocationOrders.length > 0) {
                    isMultiDragMap.value = true;
                    multiDragMapOrderIds.value = [orderId, ...sameLocationOrders.map(o => o.id)];
                    console.log(`同一位置多订单拖拽 (未分配): ${multiDragMapOrderIds.value.length} 个订单`);
                }
            }
        }

        // 创建拖拽视觉反馈 (幽灵标记)
        createGhostMarker(lngLat, order)

        // 高亮被拖拽的订单标记
        highlightDraggedSymbols()
    }

    // 处理触摸开始事件
    const handleSymbolTouchStart = (e) => {
        // 触摸事件的处理逻辑与鼠标事件类似
        if (e.points && e.points.length > 0 && e.features && e.features.length > 0) {
            // 阻止默认行为，防止滚动
            if (e.originalEvent) {
                e.originalEvent.preventDefault()
            }

            // 获取触摸点和特征
            const feature = e.features[0]

            if (!feature || !feature.properties || !feature.properties.id) return

            const featureId = feature.properties.id
            const isCluster = feature.properties.isCluster

            // 处理聚合标记
            if (isCluster) {
                console.log('触摸拖拽聚合标记，包含订单数量:', feature.properties.clusterCount)

                // 获取聚合中的所有订单ID
                let clusterOrderIds = feature.properties.clusterOrderIds
                console.log('触摸事件 - 原始聚合标记中的订单ID:', clusterOrderIds, '类型:', typeof clusterOrderIds)

                // 检查是否是字符串，如果是，尝试解析为数组
                if (typeof clusterOrderIds === 'string') {
                    try {
                        clusterOrderIds = JSON.parse(clusterOrderIds)
                        console.log('触摸事件 - 解析后的聚合订单ID:', clusterOrderIds)
                    } catch (e) {
                        // 如果不是有效的JSON，尝试按逗号分割
                        clusterOrderIds = clusterOrderIds.split(',')
                        console.log('触摸事件 - 按逗号分割后的聚合订单ID:', clusterOrderIds)
                    }
                }

                if (!clusterOrderIds || !Array.isArray(clusterOrderIds) || clusterOrderIds.length === 0) {
                    console.warn('聚合标记缺少有效的订单ID列表')
                    return
                }

                // 分析聚合中的订单状态
                const clusterAnalysis = handleMixedClusterDrag(clusterOrderIds)

                // 如果是混合状态，自动选择未分配订单
                if (clusterAnalysis.type === 'mixed') {
                    console.log('触摸事件 - 检测到混合状态聚合标记，自动选择未分配订单进行拖拽')
                    console.log(`触摸事件 - 混合状态分析: 已分配${clusterAnalysis.assignedOrders.length}单, 未分配${clusterAnalysis.unassignedOrders.length}单`)

                    // 自动选择未分配订单
                    const selection = {
                        type: 'unassigned_only',
                        orders: clusterAnalysis.unassignedOrders
                    }

                    console.log(`触摸事件 - 自动选择拖拽未分配订单: ${selection.orders.length}单`)

                    // 根据选择开始拖拽
                    startClusterDragWithSelection(selection, e)

                    return // 开始拖拽，不继续执行
                }

                // 非混合状态，使用原有逻辑
                const ordersToMove = clusterAnalysis.orders
                const firstOrder = ordersToMove[0]

                // 设置拖拽状态
                isDraggingMapSymbol.value = true

                // 判断拖拽类型
                if (firstOrder.route_id) {
                    dragType.value = 'assigned'
                    isUnassignedDrag.value = false
                    affectedRouteId.value = firstOrder.route_id
                    console.log('开始触摸拖拽已分配订单聚合')
                } else {
                    dragType.value = 'unassigned'
                    isUnassignedDrag.value = true
                    affectedRouteId.value = null
                    console.log('开始触摸拖拽未分配订单聚合')
                }

                // 禁用地图拖拽功能
                if (map.value) {
                    map.value.dragPan.disable()
                }

                // 发送事件通知其他组件拖拽开始
                eventBus.emit(EVENT_TYPES.DRAG_START, {
                    orderId: firstOrder.id,
                    routeId: firstOrder.route_id,
                    timestamp: Date.now()
                })

                // 记录原始位置信息
                const lngLat = e.lngLat
                const screenPoint = map.value.project(lngLat)

                draggedOrderInfo.value = {
                    orderId: firstOrder.id,
                    routeId: firstOrder.route_id,
                    originalLngLat: [lngLat.lng, lngLat.lat],
                    originalScreenPoint: [screenPoint.x, screenPoint.y]
                }

                // 使用分析后的订单列表
                if (ordersToMove.length > 1) {
                    isMultiDragMap.value = true
                    multiDragMapOrderIds.value = ordersToMove.map(o => o.id)
                    console.log(`聚合标记触摸拖拽 (${dragType.value}): ${multiDragMapOrderIds.value.length} 个订单`)
                }

                // 创建拖拽视觉反馈 (幽灵标记)
                createGhostMarker(lngLat, firstOrder, feature.properties.clusterCount)

                // 高亮被拖拽的订单标记
                highlightDraggedSymbols()

                return
            }

            // 处理普通订单标记
            const orderId = featureId
            // 使用 orderStore.allOrders.find 代替不存在的 getOrderById 方法
            const order = orderStore.allOrders.find(o => o.id === orderId)

            // 检查订单是否存在
            if (!order) {
                console.log('订单不存在')
                return
            }

            // 设置拖拽状态
            isDraggingMapSymbol.value = true

            // 判断拖拽类型
            if (order.route_id) {
                dragType.value = 'assigned'
                isUnassignedDrag.value = false
                affectedRouteId.value = order.route_id
                console.log(`开始触摸拖拽已分配订单: ${orderId}`)
            } else {
                dragType.value = 'unassigned'
                isUnassignedDrag.value = true
                affectedRouteId.value = null
                console.log(`开始触摸拖拽未分配订单: ${orderId}`)
            }

            // 禁用地图拖拽功能
            if (map.value) {
                map.value.dragPan.disable()
            }

            // 发送事件通知其他组件拖拽开始
            eventBus.emit(EVENT_TYPES.DRAG_START, {
                orderId: order.id,
                routeId: order.route_id,
                timestamp: Date.now()
            })

            // 记录原始位置信息
            const lngLat = e.lngLat
            const screenPoint = map.value.project(lngLat)

            draggedOrderInfo.value = {
                orderId,
                routeId: order.route_id,
                originalLngLat: [lngLat.lng, lngLat.lat],
                originalScreenPoint: [screenPoint.x, screenPoint.y]
            }

            // 检查是否有多选
            let useMultiSelect = false;
            if (orderStore.selectedOrderIds.has(orderId) && orderStore.selectedOrderIds.size > 1) {
                // 验证所有选中的订单是否属于同一路线
                const selectedOrders = Array.from(orderStore.selectedOrderIds)
                    .map(id => orderStore.allOrders.find(o => o.id === id))
                    .filter(o => o && o.route_id === affectedRouteId.value)

                if (selectedOrders.length > 1) {
                    isMultiDragMap.value = true
                    multiDragMapOrderIds.value = selectedOrders.map(o => o.id)
                    console.log(`多选拖拽: ${multiDragMapOrderIds.value.length} 个订单`)
                    useMultiSelect = true;
                }
            }

            // 如果没有多选，检查同一位置是否有多个订单
            if (!useMultiSelect) {
                if (dragType.value === 'assigned') {
                    // 已分配订单：获取当前路线的所有订单
                    const routeOrders = orderStore.getOrdersByRouteNumber(affectedRouteId.value);

                    // 查找与当前订单位置相同的其他订单
                    const sameLocationOrders = routeOrders.filter(o => {
                        if (o.id === orderId) return false; // 排除当前订单

                        // 检查位置是否相同
                        const orderLocation = o.lng_lat || o.location;
                        const currentLocation = order.lng_lat || order.location;

                        return orderLocation === currentLocation;
                    });

                    // 如果找到同一位置的其他订单，将它们添加到拖拽列表中
                    if (sameLocationOrders.length > 0) {
                        isMultiDragMap.value = true;
                        multiDragMapOrderIds.value = [orderId, ...sameLocationOrders.map(o => o.id)];
                        console.log(`同一位置多订单触摸拖拽 (已分配): ${multiDragMapOrderIds.value.length} 个订单`);
                    }
                } else {
                    // 未分配订单：查找同一位置的其他未分配订单
                    const allUnassignedOrders = orderStore.allOrders.filter(o => !o.route_id);

                    // 查找与当前订单位置相同的其他未分配订单
                    const sameLocationOrders = allUnassignedOrders.filter(o => {
                        if (o.id === orderId) return false; // 排除当前订单

                        // 检查位置是否相同
                        const orderLocation = o.lng_lat || o.location;
                        const currentLocation = order.lng_lat || order.location;

                        return orderLocation && currentLocation &&
                               orderLocation[0] === currentLocation[0] &&
                               orderLocation[1] === currentLocation[1];
                    });

                    // 如果找到同一位置的其他未分配订单，将它们添加到拖拽列表中
                    if (sameLocationOrders.length > 0) {
                        isMultiDragMap.value = true;
                        multiDragMapOrderIds.value = [orderId, ...sameLocationOrders.map(o => o.id)];
                        console.log(`同一位置多订单触摸拖拽 (未分配): ${multiDragMapOrderIds.value.length} 个订单`);
                    }
                }
            }

            // 创建拖拽视觉反馈
            createGhostMarker(lngLat, order)

            // 高亮被拖拽的订单标记
            highlightDraggedSymbols()
        }
    }

    // 处理鼠标移动事件
    const handleSymbolDrag = (e) => {
        if (!isDraggingMapSymbol.value || !draggedOrderInfo.value) return

        console.log('鼠标移动事件:', e);

        // 获取当前鼠标位置
        const point = {
            x: e.clientX,
            y: e.clientY
        }

        console.log('鼠标位置:', point);

        // 将屏幕坐标转换为地图坐标
        const mapContainer = map.value.getContainer()
        const rect = mapContainer.getBoundingClientRect()
        const mapPoint = {
            x: point.x - rect.left,
            y: point.y - rect.top
        }

        console.log('地图坐标:', mapPoint);

        // 直接更新幽灵标记位置，使用屏幕坐标
        if (ghostMarker.value) {
            // 直接使用鼠标位置，而不是转换为经纬度再转回屏幕坐标
            updateGhostMarkerPosition({
                x: point.x,
                y: point.y
            });
        } else {
            console.warn('幽灵标记不存在，无法更新位置');
        }

        // 查找潜在的放置目标
        findPotentialDropTarget(mapPoint)
    }

    // 处理触摸移动事件
    const handleSymbolTouchMove = (e) => {
        if (!isDraggingMapSymbol.value || !draggedOrderInfo.value) return

        // 阻止默认行为，防止滚动
        if (e.preventDefault) {
            e.preventDefault()
        }

        // 获取触摸点
        let point
        if (e.touches && e.touches.length > 0) {
            // 标准触摸事件
            const touch = e.touches[0]
            point = {
                x: touch.clientX,
                y: touch.clientY
            }
        } else if (e.points && e.points.length > 0) {
            // MapLibre 触摸事件
            const touch = e.points[0]
            point = {
                x: touch.x,
                y: touch.y
            }
        } else {
            return // 没有有效的触摸点
        }

        // 将屏幕坐标转换为地图坐标
        const mapContainer = map.value.getContainer()
        const rect = mapContainer.getBoundingClientRect()
        const mapPoint = {
            x: point.x - rect.left,
            y: point.y - rect.top
        }

        // 直接更新幽灵标记位置，使用屏幕坐标
        if (ghostMarker.value) {
            // 直接使用触摸点位置，而不是转换为经纬度再转回屏幕坐标
            updateGhostMarkerPosition({
                x: point.x,
                y: point.y
            });
        } else {
            console.warn('幽灵标记不存在，无法更新位置');
        }

        // 查找潜在的放置目标
        findPotentialDropTarget(mapPoint)
    }

    // 处理鼠标释放事件 - 优化版，使用与 DriverRoutePanel 相同的算法
    const handleSymbolMouseUp = (e) => {
        if (!isDraggingMapSymbol.value || !draggedOrderInfo.value) return

        console.time('handleSymbolMouseUp');

        try {
            // 获取当前鼠标位置
            const point = {
                x: e.clientX,
                y: e.clientY
            }

            // 将屏幕坐标转换为地图坐标
            const mapContainer = map.value.getContainer()
            const rect = mapContainer.getBoundingClientRect()
            const mapPoint = {
                x: point.x - rect.left,
                y: point.y - rect.top
            }

            // 确定放置目标索引
            const targetResult = determineDropTargetIndex(mapPoint)

            if (targetResult !== -1) {
                // 检查是否是未分配订单拖拽
                if (typeof targetResult === 'object' && (targetResult.type === 'unassigned_to_assigned' || targetResult.type === 'unassigned_to_route_line')) {
                    // 处理未分配订单本地分配
                    const success = handleUnassignedOrderDrag(targetResult);
                    if (success) {
                        console.log('未分配订单本地分配成功');
                    } else {
                        console.log('未分配订单本地分配失败');
                    }
                } else {
                    // 处理已分配订单重排序
                    const targetIndex = targetResult;

                    // 计算新顺序
                    const newOrderedList = calculateNewOrderSequence(targetIndex)

                    // 路线ID已经在affectedRouteId.value中

                    // 打印拖拽信息
                    if (isMultiDragMap.value) {
                        console.log(`拖拽 ${multiDragMapOrderIds.value.length} 个订单到位置 ${targetIndex + 1}`);
                        console.log('拖拽的订单IDs:', multiDragMapOrderIds.value);
                    } else {
                        console.log(`拖拽订单 ${draggedOrderInfo.value.orderId} 到位置 ${targetIndex + 1}`);
                    }

                    // 直接调用OrderStore更新本地状态
                    orderStore.updateLocalOrderSequence(affectedRouteId.value, newOrderedList);

                    // 发送 MAP_REFRESH 事件，通知地图更新标记
                    eventBus.emit(EVENT_TYPES.MAP_REFRESH, {
                        routeId: affectedRouteId.value,
                        orders: newOrderedList,
                        source: 'map-drag',
                        timestamp: Date.now()
                    });

                    // 发送 ORDERS_UPDATED 事件，通知 DriverRoutePanel 更新
                    const routeStore = window.routeStore;
                    const routeName = routeStore?.getRouteById ?
                        routeStore.getRouteById(affectedRouteId.value)?.name :
                        `路线 ${affectedRouteId.value}`;

                    eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                        orders: newOrderedList,
                        routeId: affectedRouteId.value,
                        routeName: routeName,
                        isDragging: true,
                        isMapDrag: true,
                        timestamp: Date.now()
                    });

                    // 触发重新绘制真实路线
                    if (window.mapRouteManagement && window.mapRouteManagement.drawRealisticRoute) {
                        console.log('🗺️ 地图拖拽结束: 触发重新绘制真实路线, routeId:', affectedRouteId.value)
                        try {
                            // 获取路线信息
                            const route = routeStore?.getRouteById ? routeStore.getRouteById(affectedRouteId.value) : null
                            const driverId = route?.driver || route?.driverId

                            console.log('📋 地图拖拽使用新订单顺序:', newOrderedList.map(o => ({ id: o.id, stop_no: o.stop_no, name: o.name })))

                            // 传递当前的司机ID和新的订单顺序
                            window.mapRouteManagement.drawRealisticRoute(affectedRouteId.value, driverId, newOrderedList)
                            console.log('✅ 地图拖拽真实路线重绘已触发')
                        } catch (error) {
                            console.error('❌ 地图拖拽重新绘制真实路线失败:', error)
                        }
                    } else {
                        console.warn('⚠️ mapRouteManagement 不可用，无法重新绘制真实路线')
                    }

                    // 不调用 API，只显示提示
                    console.log('订单顺序已更新，请点击"保存路线顺序"按钮保存更改');
                }
            } else {
                console.log('未拖拽到任何标记上，不执行操作');
            }
        } finally {
            // 清理拖拽状态
            cleanupDragState();
            console.timeEnd('handleSymbolMouseUp');
        }
    }

    // 处理触摸结束事件 - 优化版，使用与 DriverRoutePanel 相同的算法
    const handleSymbolTouchEnd = () => {
        if (!isDraggingMapSymbol.value || !draggedOrderInfo.value) return

        console.time('handleSymbolTouchEnd');

        try {
            // 确定放置目标索引 - 使用最后记录的触摸位置
            const targetResult = determineDropTargetIndex({
                x: ghostMarker.value ? ghostMarker.value._pos.x : 0,
                y: ghostMarker.value ? ghostMarker.value._pos.y : 0
            })

            if (targetResult !== -1) {
                // 检查是否是未分配订单拖拽
                if (typeof targetResult === 'object' && (targetResult.type === 'unassigned_to_assigned' || targetResult.type === 'unassigned_to_route_line')) {
                    // 处理未分配订单本地分配
                    const success = handleUnassignedOrderDrag(targetResult);
                    if (success) {
                        console.log('未分配订单本地分配成功');
                    } else {
                        console.log('未分配订单本地分配失败');
                    }
                } else {
                    // 处理已分配订单重排序
                    const targetIndex = targetResult;

                    // 计算新顺序
                    const newOrderedList = calculateNewOrderSequence(targetIndex)

                    // 路线ID已经在affectedRouteId.value中

                    // 打印拖拽信息
                    if (isMultiDragMap.value) {
                        console.log(`拖拽 ${multiDragMapOrderIds.value.length} 个订单到位置 ${targetIndex + 1}`);
                        console.log('拖拽的订单IDs:', multiDragMapOrderIds.value);
                    } else {
                        console.log(`拖拽订单 ${draggedOrderInfo.value.orderId} 到位置 ${targetIndex + 1}`);
                    }

                    // 直接调用OrderStore更新本地状态
                    orderStore.updateLocalOrderSequence(affectedRouteId.value, newOrderedList);

                    // 发送 MAP_REFRESH 事件，通知地图更新标记
                    eventBus.emit(EVENT_TYPES.MAP_REFRESH, {
                        routeId: affectedRouteId.value,
                        orders: newOrderedList,
                        source: 'map-drag',
                        timestamp: Date.now()
                    });

                    // 发送 ORDERS_UPDATED 事件，通知 DriverRoutePanel 更新
                    const routeStore = window.routeStore;
                    const routeName = routeStore?.getRouteById ?
                        routeStore.getRouteById(affectedRouteId.value)?.name :
                        `路线 ${affectedRouteId.value}`;

                    eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                        orders: newOrderedList,
                        routeId: affectedRouteId.value,
                        routeName: routeName,
                        isDragging: true,
                        isMapDrag: true,
                        timestamp: Date.now()
                    });

                    // 触发重新绘制真实路线
                    if (window.mapRouteManagement && window.mapRouteManagement.drawRealisticRoute) {
                        console.log('🗺️ 地图触摸拖拽结束: 触发重新绘制真实路线, routeId:', affectedRouteId.value)
                        try {
                            // 获取路线信息
                            const route = routeStore?.getRouteById ? routeStore.getRouteById(affectedRouteId.value) : null
                            const driverId = route?.driver || route?.driverId

                            console.log('📋 地图触摸拖拽使用新订单顺序:', newOrderedList.map(o => ({ id: o.id, stop_no: o.stop_no, name: o.name })))

                            // 传递当前的司机ID和新的订单顺序
                            window.mapRouteManagement.drawRealisticRoute(affectedRouteId.value, driverId, newOrderedList)
                            console.log('✅ 地图触摸拖拽真实路线重绘已触发')
                        } catch (error) {
                            console.error('❌ 地图触摸拖拽重新绘制真实路线失败:', error)
                        }
                    } else {
                        console.warn('⚠️ mapRouteManagement 不可用，无法重新绘制真实路线')
                    }

                    // 不调用 API，只显示提示
                    console.log('订单顺序已更新，请点击"保存路线顺序"按钮保存更改');
                }
            } else {
                console.log('未拖拽到任何标记上，不执行操作');
            }
        } finally {
            // 清理拖拽状态
            cleanupDragState();
            console.timeEnd('handleSymbolTouchEnd');
        }
    }

    // 创建幽灵标记 - 使用 DOM 元素直接添加到页面，而不是使用 MapLibre Marker
    const createGhostMarker = (lngLat, order, clusterCount = null) => {
        console.log('创建幽灵标记:', { lngLat, order, clusterCount });

        // 移除现有的幽灵标记（如果有）
        if (ghostMarker.value) {
            if (ghostMarker.value.remove) {
                ghostMarker.value.remove();
            } else if (ghostMarker.value.parentNode) {
                ghostMarker.value.parentNode.removeChild(ghostMarker.value);
            }
        }

        // 创建一个 DOM 元素作为幽灵标记
        const el = document.createElement('div');
        el.className = 'ghost-marker';
        el.style.position = 'fixed'; // 使用 fixed 定位，相对于视口
        el.style.zIndex = '9999'; // 确保在最上层
        el.style.pointerEvents = 'none'; // 确保不会干扰鼠标事件
        el.style.backgroundColor = 'rgba(255, 59, 92, 0.7)'; // 半透明红色背景
        el.style.color = 'white'; // 白色文字
        el.style.padding = '5px 10px'; // 内边距
        el.style.borderRadius = '15px'; // 圆角
        el.style.fontWeight = 'bold'; // 粗体文字
        el.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.3)'; // 阴影效果

        // 如果是聚合标记拖拽
        if (clusterCount !== null) {
            el.classList.add('cluster-drag');

            // 设置文本内容为"拖拽几单"
            el.textContent = `${clusterCount}单`;

            // 添加提示信息
            el.title = `拖拽聚合的 ${clusterCount} 个订单`;

            // 添加特殊样式
            el.style.backgroundColor = 'rgba(255, 59, 92, 0.8)'; // 更深的红色
            el.style.border = '2px solid white'; // 白色边框
        }
        // 根据是否是多选设置不同的样式
        else if (isMultiDragMap.value) {
            el.classList.add('multi-drag');

            // 获取拖拽的订单数量
            const count = multiDragMapOrderIds.value.length;

            // 设置文本内容为"拖拽几单"
            el.textContent = `${count}单`;

            // 添加提示信息
            el.title = `拖拽 ${count} 个订单`;

            // 如果是同一位置的多个订单，添加特殊样式
            const isSameLocation = !orderStore.selectedOrderIds.has(order.id) ||
                                  orderStore.selectedOrderIds.size <= 1;

            if (isSameLocation) {
                el.classList.add('same-location');
                el.title = `拖拽同一位置的 ${count} 个订单`;
                el.style.backgroundColor = 'rgba(0, 150, 255, 0.7)'; // 蓝色背景
            }
        } else {
            el.textContent = `1单`;
            el.title = `拖拽订单 #${order.stop_no || ''}`;
        }

        // 将元素添加到 body
        document.body.appendChild(el);

        // 保存元素引用
        ghostMarker.value = el;

        console.log('幽灵标记创建成功:', el);

        // 初始化位置
        const point = map.value.project(lngLat);
        const mapContainer = map.value.getContainer();
        const rect = mapContainer.getBoundingClientRect();

        el.style.left = `${point.x + rect.left}px`;
        el.style.top = `${point.y + rect.top}px`;

        console.log('创建幽灵标记:', el);
    }

    // 更新幽灵标记位置 - 适用于 DOM 元素
    const updateGhostMarkerPosition = (lngLat) => {
        if (!ghostMarker.value) {
            console.warn('幽灵标记不存在，无法更新位置');
            return;
        }

        // 保存位置信息，用于后续处理
        ghostMarker.value._pos = {
            x: lngLat.x,
            y: lngLat.y
        };

        // 如果传入的是经纬度，转换为屏幕坐标
        if (lngLat && lngLat.lng !== undefined && lngLat.lat !== undefined) {
            const mapContainer = map.value.getContainer();
            const rect = mapContainer.getBoundingClientRect();
            const point = map.value.project(lngLat);

            // 更新位置
            ghostMarker.value.style.left = `${point.x + rect.left}px`;
            ghostMarker.value.style.top = `${point.y + rect.top}px`;

            console.log('更新幽灵标记位置 (经纬度):', lngLat, '屏幕坐标:', {
                x: point.x + rect.left,
                y: point.y + rect.top
            });
        }
        // 如果传入的是屏幕坐标
        else if (lngLat && lngLat.x !== undefined && lngLat.y !== undefined) {
            // 直接更新位置
            ghostMarker.value.style.left = `${lngLat.x}px`;
            ghostMarker.value.style.top = `${lngLat.y}px`;

            console.log('更新幽灵标记位置 (屏幕坐标):', lngLat);
        }

        // 确保幽灵标记在最上层
        ghostMarker.value.style.zIndex = '9999';

        // 确保幽灵标记可见
        ghostMarker.value.style.display = 'flex';
        ghostMarker.value.style.position = 'fixed';
    }

    // 高亮被拖拽的订单标记
    const highlightDraggedSymbols = () => {
        // 获取要高亮的订单ID列表
        const orderIds = isMultiDragMap.value
            ? multiDragMapOrderIds.value
            : [draggedOrderInfo.value.orderId]

        // 为每个订单创建临时的高亮样式
        // 由于不能使用 feature-state，我们将使用数据源更新的方式
        // 在实际应用中，可以考虑添加一个专门的拖拽中图层

        // 这里我们只是记录被拖拽的订单ID，实际的视觉效果由幽灵标记提供
        console.log('拖拽订单:', orderIds)
    }

    // 高亮放置目标
    const highlightDropTarget = (targetOrderId) => {
        // 记录高亮目标ID
        highlightedTargetId.value = targetOrderId

        if (targetOrderId) {
            // 检查是否是路线线高亮
            if (targetOrderId.startsWith('route-line-')) {
                const routeId = targetOrderId.replace('route-line-', '');
                highlightRouteLine(routeId);
                console.log('高亮路线线:', routeId);
            } else {
                // 清除路线线高亮
                clearRouteLineHighlight();
                console.log('高亮目标订单:', targetOrderId);
            }
        } else {
            // 清除所有高亮
            clearRouteLineHighlight();
        }
    }

    // 高亮路线线
    const highlightRouteLine = (routeId) => {
        if (!map.value) return;

        const layerId = `route-layer-${routeId}`;

        try {
            // 检查图层是否存在
            if (map.value.getLayer(layerId)) {
                // 更新路线样式为高亮状态
                map.value.setPaintProperty(layerId, 'line-color', '#ff3b5c'); // 红色高亮
                map.value.setPaintProperty(layerId, 'line-width', 6); // 加粗
                map.value.setPaintProperty(layerId, 'line-opacity', 1.0); // 完全不透明

                // 将路线图层移到标记图层下方但在其他路线之上
                const allRouteLayers = getRouteLayerIds();
                const otherRouteLayers = allRouteLayers.filter(id => id !== layerId);

                // 先移动到所有其他路线图层之上
                if (otherRouteLayers.length > 0) {
                    const topOtherRouteLayer = otherRouteLayers[otherRouteLayers.length - 1];
                    map.value.moveLayer(layerId, topOtherRouteLayer);
                }

                console.log(`路线 ${routeId} 已高亮显示`);
            }
        } catch (error) {
            console.error('高亮路线时出错:', error);
        }
    }

    // 清除路线线高亮
    const clearRouteLineHighlight = () => {
        if (!map.value) return;

        try {
            // 获取所有路线图层并恢复默认样式
            const routeLayers = getRouteLayerIds();

            routeLayers.forEach(layerId => {
                if (map.value.getLayer(layerId)) {
                    // 恢复默认样式
                    const routeId = layerId.replace('route-layer-', '');

                    // 获取路线颜色（可以从路线存储中获取）
                    const routeStore = window.routeStore;
                    let routeColor = '#007bff'; // 默认蓝色

                    if (routeStore?.getRouteById) {
                        const route = routeStore.getRouteById(routeId);
                        if (route && route.color) {
                            routeColor = route.color;
                        }
                    }

                    map.value.setPaintProperty(layerId, 'line-color', routeColor);
                    map.value.setPaintProperty(layerId, 'line-width', 3); // 默认宽度
                    map.value.setPaintProperty(layerId, 'line-opacity', 0.8); // 默认透明度
                }
            });

            console.log('已清除所有路线高亮');
        } catch (error) {
            console.error('清除路线高亮时出错:', error);
        }
    }

    // 查找潜在放置目标 - 支持拖拽到标记和路线线上
    const findPotentialDropTarget = throttle((mapPoint) => {
        console.log('查找潜在放置目标:', mapPoint, '拖拽类型:', dragType.value);

        // 首先检查是否有标记在鼠标下
        const targetFeatures = map.value.queryRenderedFeatures(
            [mapPoint.x, mapPoint.y],
            { layers: ['unassigned-orders-layer', 'assigned-orders-layer', 'selected-orders-layer'] }
        );

        console.log('找到的标记特征:', targetFeatures?.length || 0);

        // 如果找到标记，优先处理标记
        if (targetFeatures && targetFeatures.length > 0) {
            // 处理标记拖拽（原有逻辑）
            handleMarkerDropTarget(targetFeatures[0]);
            return;
        }

        // 如果是未分配订单拖拽，检查是否拖拽到路线线上
        if (dragType.value === 'unassigned') {
            console.log('检查路线线拖拽...');

            const routeLayerIds = getRouteLayerIds();
            console.log('可用的路线图层:', routeLayerIds);

            if (routeLayerIds.length > 0) {
                const routeFeatures = map.value.queryRenderedFeatures(
                    [mapPoint.x, mapPoint.y],
                    { layers: routeLayerIds }
                );

                console.log('找到的路线特征:', routeFeatures?.length || 0);

                if (routeFeatures && routeFeatures.length > 0) {
                    console.log('路线特征详情:', routeFeatures[0]);
                    // 处理路线线拖拽
                    handleRouteLineDropTarget(routeFeatures[0]);
                    return;
                }
            }
        }

        // 如果没有找到任何目标，清除高亮
        console.log('未找到任何拖拽目标，清除高亮');
        highlightDropTarget(null);
    }, 50) // 减少节流时间，提高响应性

    // 获取所有路线图层ID
    const getRouteLayerIds = () => {
        if (!map.value) {
            console.warn('地图对象不存在');
            return [];
        }

        try {
            const style = map.value.getStyle();
            if (!style || !style.layers) {
                console.warn('地图样式或图层不存在');
                return [];
            }

            const routeLayerIds = style.layers
                .filter(layer => layer.id.startsWith('route-layer-'))
                .map(layer => layer.id);

            console.log('获取到的路线图层ID:', routeLayerIds);
            return routeLayerIds;
        } catch (error) {
            console.error('获取路线图层ID时出错:', error);
            return [];
        }
    }

    // 处理标记拖拽目标（原有逻辑）
    const handleMarkerDropTarget = (targetFeature) => {
        if (!targetFeature || !targetFeature.properties || !targetFeature.properties.id) {
            highlightDropTarget(null);
            return;
        }

        // 检查是否是聚合标记
        const isCluster = targetFeature.properties.isCluster;
        console.log('潜在放置目标是否是聚合标记:', isCluster);

        if (isCluster) {
            // 处理聚合标记
            let clusterOrderIds = targetFeature.properties.clusterOrderIds;
            console.log('findPotentialDropTarget - 原始聚合标记中的订单ID:', clusterOrderIds, '类型:', typeof clusterOrderIds);

            // 检查是否是字符串，如果是，尝试解析为数组
            if (typeof clusterOrderIds === 'string') {
                try {
                    clusterOrderIds = JSON.parse(clusterOrderIds);
                    console.log('findPotentialDropTarget - 解析后的聚合订单ID:', clusterOrderIds);
                } catch (e) {
                    // 如果不是有效的JSON，尝试按逗号分割
                    clusterOrderIds = clusterOrderIds.split(',');
                    console.log('findPotentialDropTarget - 按逗号分割后的聚合订单ID:', clusterOrderIds);
                }
            }

            if (!clusterOrderIds || !Array.isArray(clusterOrderIds) || clusterOrderIds.length === 0) {
                highlightDropTarget(null);
                return;
            }

            // 获取聚合中的第一个有效订单
            const firstOrderId = clusterOrderIds[0];

            // 检查是否是正在拖拽的订单，如果是则不高亮
            if (isMultiDragMap.value && multiDragMapOrderIds.value.includes(firstOrderId)) {
                highlightDropTarget(null);
                return;
            }

            if (!isMultiDragMap.value && firstOrderId === draggedOrderInfo.value.orderId) {
                highlightDropTarget(null);
                return;
            }

            // 检查目标订单是否在当前路线中（对于已分配订单拖拽）
            // 或者是否是已分配订单（对于未分配订单拖拽）
            if (dragType.value === 'assigned') {
                const routeOrders = orderStore.getOrdersByRouteNumber(affectedRouteId.value);
                const targetOrder = routeOrders.find(order => order.id === firstOrderId);
                if (targetOrder) {
                    highlightDropTarget(firstOrderId);
                } else {
                    highlightDropTarget(null);
                }
            } else {
                // 未分配订单拖拽：检查目标是否是已分配订单
                const targetOrder = orderStore.allOrders.find(o => o.id === firstOrderId);
                if (targetOrder && targetOrder.route_id) {
                    highlightDropTarget(firstOrderId);
                } else {
                    highlightDropTarget(null);
                }
            }
        } else {
            // 处理普通订单标记
            const targetOrderId = targetFeature.properties.id;

            // 检查是否是正在拖拽的订单，如果是则不高亮
            if (isMultiDragMap.value && multiDragMapOrderIds.value.includes(targetOrderId)) {
                highlightDropTarget(null);
                return;
            }

            if (!isMultiDragMap.value && targetOrderId === draggedOrderInfo.value.orderId) {
                highlightDropTarget(null);
                return;
            }

            // 检查目标订单是否符合拖拽条件
            if (dragType.value === 'assigned') {
                const routeOrders = orderStore.getOrdersByRouteNumber(affectedRouteId.value);
                const targetOrder = routeOrders.find(order => order.id === targetOrderId);
                if (targetOrder) {
                    highlightDropTarget(targetOrderId);
                } else {
                    highlightDropTarget(null);
                }
            } else {
                // 未分配订单拖拽：检查目标是否是已分配订单
                const targetOrder = orderStore.allOrders.find(o => o.id === targetOrderId);
                if (targetOrder && targetOrder.route_id) {
                    highlightDropTarget(targetOrderId);
                } else {
                    highlightDropTarget(null);
                }
            }
        }
    }

    // 处理路线线拖拽目标
    const handleRouteLineDropTarget = (routeFeature) => {
        console.log('检测到拖拽到路线线上:', routeFeature);

        // 从路线图层ID中提取路线ID
        const layerId = routeFeature.layer.id; // 例如: 'route-layer-1'
        const routeId = layerId.replace('route-layer-', '');

        console.log(`拖拽到路线 ${routeId} 的线上`);

        // 高亮整条路线（可以通过特殊的高亮方式表示）
        highlightDropTarget(`route-line-${routeId}`);
    }

    // 确定放置目标索引 - 支持未分配订单拖拽到已分配订单
    const determineDropTargetIndex = (screenPoint) => {
        console.log(`确定放置目标索引 - 拖拽类型: ${dragType.value}`);

        if (dragType.value === 'unassigned') {
            // 未分配订单拖拽：只需要找到目标已分配订单
            return determineUnassignedDropTarget(screenPoint);
        } else {
            // 已分配订单拖拽：使用原有逻辑
            return determineAssignedDropTarget(screenPoint);
        }
    }

    // 处理未分配订单拖拽到已分配订单的逻辑
    const determineUnassignedDropTarget = (screenPoint) => {
        console.log('处理未分配订单拖拽');

        // 首先检查是否有标记被点击
        const targetFeatures = map.value.queryRenderedFeatures(
            [screenPoint.x, screenPoint.y],
            { layers: ['unassigned-orders-layer', 'assigned-orders-layer', 'selected-orders-layer'] }
        );

        // 如果找到标记，处理标记拖拽
        if (targetFeatures && targetFeatures.length > 0) {
            return handleUnassignedToMarkerDrop(targetFeatures[0]);
        }

        // 如果没有找到标记，检查是否拖拽到路线线上
        const routeFeatures = map.value.queryRenderedFeatures(
            [screenPoint.x, screenPoint.y],
            { layers: getRouteLayerIds() }
        );

        if (routeFeatures && routeFeatures.length > 0) {
            return handleUnassignedToRouteLineDrop(routeFeatures[0], screenPoint);
        }

        console.log('未分配订单拖拽：未拖拽到任何有效目标上，不执行操作');
        return -1;
    }

    // 处理未分配订单拖拽到标记的逻辑
    const handleUnassignedToMarkerDrop = (targetFeature) => {
        if (!targetFeature || !targetFeature.properties || !targetFeature.properties.id) {
            console.log('未分配订单拖拽：无法识别目标标记，不执行操作');
            return -1;
        }

        let targetOrderId;
        const isCluster = targetFeature.properties.isCluster;

        if (isCluster) {
            // 处理聚合标记
            let clusterOrderIds = targetFeature.properties.clusterOrderIds;
            if (typeof clusterOrderIds === 'string') {
                try {
                    clusterOrderIds = JSON.parse(clusterOrderIds);
                } catch (e) {
                    clusterOrderIds = clusterOrderIds.split(',');
                }
            }

            if (!clusterOrderIds || !Array.isArray(clusterOrderIds) || clusterOrderIds.length === 0) {
                console.log('未分配订单拖拽：聚合标记缺少有效的订单ID列表');
                return -1;
            }

            targetOrderId = clusterOrderIds[0];
        } else {
            targetOrderId = targetFeature.properties.id;
        }

        // 检查目标订单是否是已分配订单
        const targetOrder = orderStore.allOrders.find(o => o.id === targetOrderId);
        if (!targetOrder || !targetOrder.route_id) {
            console.log('未分配订单拖拽：只能拖拽到已分配订单上');
            return -1;
        }

        console.log(`未分配订单拖拽到标记：目标订单 ${targetOrderId}，路线 ${targetOrder.route_id}`);

        // 返回目标订单信息，用于后续的分配处理
        return {
            type: 'unassigned_to_assigned',
            targetOrderId: targetOrderId,
            targetRouteId: targetOrder.route_id
        };
    }

    // 处理未分配订单拖拽到路线线的逻辑
    const handleUnassignedToRouteLineDrop = (routeFeature, screenPoint) => {
        console.log('处理未分配订单拖拽到路线线');

        // 从路线图层ID中提取路线ID
        const layerId = routeFeature.layer.id; // 例如: 'route-layer-1'
        const routeId = layerId.replace('route-layer-', '');

        console.log(`拖拽到路线 ${routeId} 的线上`);

        // 获取该路线的所有订单，找到最大的stop_no
        const routeOrders = orderStore.getOrdersByRouteNumber(routeId) || [];

        if (routeOrders.length === 0) {
            console.log(`路线 ${routeId} 没有订单，将作为第一个订单添加`);
            return {
                type: 'unassigned_to_route_line',
                targetRouteId: routeId,
                insertPosition: 'end' // 添加到路线末尾
            };
        }

        // 找到该位置最近的已分配订单，插入到其后面
        const nearestOrder = findNearestOrderOnRoute(routeOrders, screenPoint);

        if (nearestOrder) {
            console.log(`找到最近的订单 ${nearestOrder.id} (stop ${nearestOrder.stop_no})，将插入到其后面`);
            return {
                type: 'unassigned_to_route_line',
                targetRouteId: routeId,
                insertAfterOrderId: nearestOrder.id,
                insertAfterStopNo: nearestOrder.stop_no,
                insertPosition: 'after_order'
            };
        } else {
            console.log(`无法找到最近的订单，将添加到路线末尾`);
            return {
                type: 'unassigned_to_route_line',
                targetRouteId: routeId,
                insertPosition: 'end'
            };
        }
    }

    // 在路线上找到最近的订单
    const findNearestOrderOnRoute = (routeOrders, screenPoint) => {
        let nearestOrder = null;
        let minDistance = Infinity;

        routeOrders.forEach(order => {
            if (order.lng_lat && order.lng_lat.length >= 2) {
                // 将订单坐标转换为屏幕坐标
                const orderScreenPoint = map.value.project([order.lng_lat[1], order.lng_lat[0]]);

                // 计算距离
                const distance = Math.sqrt(
                    Math.pow(screenPoint.x - orderScreenPoint.x, 2) +
                    Math.pow(screenPoint.y - orderScreenPoint.y, 2)
                );

                if (distance < minDistance) {
                    minDistance = distance;
                    nearestOrder = order;
                }
            }
        });

        console.log(`最近的订单距离: ${minDistance}px`);
        return nearestOrder;
    }

    // 处理已分配订单拖拽的逻辑（原有逻辑）
    const determineAssignedDropTarget = (screenPoint) => {
        // 获取当前路线的所有订单（按 stop_no 排序）
        const routeOrders = orderStore.getOrdersByRouteNumber(affectedRouteId.value)
            .filter(order => {
                // 排除正在拖拽的订单
                if (isMultiDragMap.value) {
                    return !multiDragMapOrderIds.value.includes(order.id)
                }
                return order.id !== draggedOrderInfo.value.orderId
            })
            .sort((a, b) => a.stop_no - b.stop_no)

        console.log('确定放置目标索引 - 路线订单数量:', routeOrders.length)
        console.log('确定放置目标索引 - 拖拽订单数量:', isMultiDragMap.value ? multiDragMapOrderIds.value.length : 1)

        if (routeOrders.length === 0) {
            // 如果路线上没有其他订单，放在第一位
            return 0
        }

        // 检查是否有标记被点击 - 使用 map.queryRenderedFeatures
        const targetFeatures = map.value.queryRenderedFeatures(
            [screenPoint.x, screenPoint.y],
            { layers: ['unassigned-orders-layer', 'assigned-orders-layer', 'selected-orders-layer'] }
        );

        // 如果没有找到任何标记，则不执行任何操作
        if (!targetFeatures || targetFeatures.length === 0) {
            console.log('未拖拽到任何标记上，不执行操作');
            return -1; // 返回 -1 表示不执行任何操作
        }

        // 获取被点击的标记对应的订单
        const targetFeature = targetFeatures[0];
        if (!targetFeature || !targetFeature.properties || !targetFeature.properties.id) {
            console.log('无法识别目标标记，不执行操作');
            return -1;
        }

        // 检查是否是聚合标记
        const isCluster = targetFeature.properties.isCluster;
        console.log('放置目标是否是聚合标记:', isCluster);

        let targetOrderId;

        if (isCluster) {
            // 处理聚合标记
            let clusterOrderIds = targetFeature.properties.clusterOrderIds;
            console.log('determineDropTargetIndex - 原始聚合标记中的订单ID:', clusterOrderIds, '类型:', typeof clusterOrderIds);

            // 检查是否是字符串，如果是，尝试解析为数组
            if (typeof clusterOrderIds === 'string') {
                try {
                    clusterOrderIds = JSON.parse(clusterOrderIds);
                    console.log('determineDropTargetIndex - 解析后的聚合订单ID:', clusterOrderIds);
                } catch (e) {
                    // 如果不是有效的JSON，尝试按逗号分割
                    clusterOrderIds = clusterOrderIds.split(',');
                    console.log('determineDropTargetIndex - 按逗号分割后的聚合订单ID:', clusterOrderIds);
                }
            }

            if (!clusterOrderIds || !Array.isArray(clusterOrderIds) || clusterOrderIds.length === 0) {
                console.log('聚合标记缺少有效的订单ID列表，不执行操作');
                return -1;
            }

            // 获取聚合中的第一个有效订单
            targetOrderId = clusterOrderIds[0];
            console.log('使用聚合标记中的第一个订单作为目标:', targetOrderId);
        } else {
            // 处理普通订单标记
            targetOrderId = targetFeature.properties.id;
        }

        // 检查目标订单是否在当前路线中
        const targetOrder = routeOrders.find(order => order.id === targetOrderId);
        if (!targetOrder) {
            console.log('目标标记不在当前路线中，不执行操作');
            return -1;
        }

        // 找到目标订单在路线中的位置
        const targetIndex = routeOrders.findIndex(order => order.id === targetOrderId);

        // 获取拖拽的订单在原始列表中的位置
        const draggedOrderIndex = orderStore.getOrdersByRouteNumber(affectedRouteId.value)
            .findIndex(order => {
                if (isMultiDragMap.value) {
                    return multiDragMapOrderIds.value.includes(order.id);
                }
                return order.id === draggedOrderInfo.value.orderId;
            });

        // 地图拖拽限制：只允许从高 stop_no 拖拽到低 stop_no
        // 获取拖拽订单的 stop_no
        let draggedStopNo;
        if (isMultiDragMap.value) {
            // 多选拖拽：获取最小的 stop_no
            const draggedOrders = multiDragMapOrderIds.value
                .map(id => routeOrders.find(o => o.id === id))
                .filter(Boolean);
            draggedStopNo = Math.min(...draggedOrders.map(o => o.stop_no));
        } else {
            // 单选拖拽
            const draggedOrder = routeOrders.find(o => o.id === draggedOrderInfo.value.orderId);
            draggedStopNo = draggedOrder ? draggedOrder.stop_no : Infinity;
        }

        // 获取目标订单的 stop_no
        const targetStopNo = targetOrder.stop_no;

        // 检查是否违反拖拽限制
        if (draggedStopNo <= targetStopNo) {
            console.log(`地图拖拽限制: 不允许从 stop_no ${draggedStopNo} 拖拽到 stop_no ${targetStopNo}（只允许从高 stop_no 拖拽到低 stop_no）`);
            return -1; // 返回 -1 表示不执行操作
        }

        // 检查目标地址是否有多个订单
        const targetLocation = targetOrder.lng_lat || targetOrder.location;

        // 获取要拖拽的订单ID列表
        const draggedOrderIds = isMultiDragMap.value
            ? multiDragMapOrderIds.value
            : [draggedOrderInfo.value.orderId];

        // 查找与目标订单位置相同的所有订单（排除正在拖拽的订单）
        const sameLocationOrders = routeOrders.filter(order => {
            // 排除正在拖拽的订单
            if (draggedOrderIds.includes(order.id)) return false;

            const orderLocation = order.lng_lat || order.location;
            return orderLocation && targetLocation &&
                   orderLocation[0] === targetLocation[0] &&
                   orderLocation[1] === targetLocation[1];
        });

        if (sameLocationOrders.length > 0) {
            // 目标地址有多个订单，找到这个地址的第一个订单的位置
            // 逻辑：插入到该地址的第一个订单位置，该地址的所有订单一起被推后
            const firstOrderAtLocation = sameLocationOrders
                .sort((a, b) => a.stop_no - b.stop_no)[0];
            const firstOrderIndex = routeOrders.findIndex(order => order.id === firstOrderAtLocation.id);

            console.log(`拖拽到多订单地址: 目标地址有 ${sameLocationOrders.length + 1} 个订单（包括目标），将插入到第一个订单 ${firstOrderAtLocation.id} 的位置 ${firstOrderIndex + 1}，该地址所有订单一起被推后`);
            return firstOrderIndex; // 插入到第一个订单的位置
        } else {
            // 目标地址只有一个订单（就是目标订单本身），使用原来的逻辑
            console.log(`拖拽订单 ${draggedOrderInfo.value.orderId} 从位置 ${draggedOrderIndex + 1} 到目标订单 ${targetOrderId} 的位置 ${targetIndex + 1}`);
            return targetIndex;
        }
    }

    // 计算新的订单顺序 - 优化版，使用与 DriverRoutePanel 相同的算法
    const calculateNewOrderSequence = (targetIndex) => {
        console.time('calculateNewOrderSequence');

        // 获取当前路线的所有订单（按 stop_no 排序）
        const allRouteOrders = orderStore.getOrdersByRouteNumber(affectedRouteId.value)
            .sort((a, b) => a.stop_no - b.stop_no);

        // 创建一个新的订单数组副本，避免直接修改原数组
        let newOrderedList = [...allRouteOrders];

        if (isMultiDragMap.value) {
            // 多选拖拽：保持选中订单的相对顺序

            // 1. 获取要拖拽的订单，并按原始顺序排序
            const draggedOrders = multiDragMapOrderIds.value
                .map(id => allRouteOrders.find(o => o.id === id))
                .filter(Boolean)
                .sort((a, b) => a.stop_no - b.stop_no);

            console.log('要拖拽的订单:', draggedOrders.map(o => ({ id: o.id, stop_no: o.stop_no })));

            // 2. 从列表中移除这些订单
            newOrderedList = newOrderedList.filter(order =>
                !multiDragMapOrderIds.value.includes(order.id)
            );

            // 3. 在目标位置插入这些订单
            newOrderedList.splice(targetIndex, 0, ...draggedOrders);

            console.log('多选拖拽 - 拖拽订单数量:', draggedOrders.length);
            console.log('多选拖拽 - 目标索引:', targetIndex);
            console.log('多选拖拽 - 新订单列表长度:', newOrderedList.length);
        } else {
            // 单选拖拽

            // 1. 找到要拖拽的订单
            const draggedOrderIndex = newOrderedList.findIndex(
                o => o.id === draggedOrderInfo.value.orderId
            );

            if (draggedOrderIndex !== -1) {
                // 2. 获取要拖拽的订单
                const draggedOrder = newOrderedList[draggedOrderIndex];

                // 3. 从列表中移除该订单
                newOrderedList.splice(draggedOrderIndex, 1);

                // 4. 使用传入的目标索引
                // determineDropTargetIndex 函数已经处理了位置调整
                const actualTargetIndex = targetIndex;

                // 5. 在目标位置插入该订单
                newOrderedList.splice(actualTargetIndex, 0, draggedOrder);

                console.log('单选拖拽 - 拖拽订单:', draggedOrder.id);
                console.log('单选拖拽 - 目标索引:', actualTargetIndex);
            }
        }

        // 更新 stop_no
        newOrderedList.forEach((order, index) => {
            order.stop_no = index + 1;
        });

        // 打印新的订单顺序，方便调试
        console.log('新的订单顺序:', newOrderedList.map(order => ({
            id: order.id,
            stop_no: order.stop_no
        })));

        console.timeEnd('calculateNewOrderSequence');
        return newOrderedList;
    }

    // 处理未分配订单拖拽到已分配订单的本地分配逻辑（不调用API）
    const handleUnassignedOrderDrag = (targetInfo) => {
        try {
            console.log('🎯 开始处理未分配订单本地分配:', targetInfo);

            // 获取要分配的未分配订单
            const draggedOrderIds = isMultiDragMap.value
                ? multiDragMapOrderIds.value
                : [draggedOrderInfo.value.orderId];

            console.log('📋 拖拽的订单ID列表:', draggedOrderIds);

            const draggedOrders = draggedOrderIds
                .map(id => {
                    const order = orderStore.allOrders.find(o => o.id === id);
                    console.log(`🔍 查找订单 ${id}:`, order ? `找到，route_id=${order.route_id}` : '未找到');
                    return order;
                })
                .filter(o => {
                    if (!o) return false;
                    const isUnassigned = !o.route_id;
                    console.log(`✅ 订单 ${o.id} 是否未分配:`, isUnassigned);
                    return isUnassigned;
                });

            console.log('📦 有效的未分配订单:', draggedOrders.map(o => ({ id: o.id, name: o.name || o.address })));

            if (draggedOrders.length === 0) {
                console.warn('⚠️ 没有找到有效的未分配订单');
                return false;
            }

            // 获取目标路线信息
            const routeStore = window.routeStore;
            let targetDriverId = null;

            // 方法1：从 routeStore 获取
            if (routeStore?.getRouteById) {
                const targetRoute = routeStore.getRouteById(targetInfo.targetRouteId);
                targetDriverId = targetRoute?.driver || targetRoute?.driverId;
                console.log(`📍 从 routeStore 获取司机ID: ${targetDriverId}`);
            }

            // 方法2：从目标订单获取司机ID（如果方法1失败）
            if (!targetDriverId) {
                const targetOrder = orderStore.allOrders.find(o => o.id === targetInfo.targetOrderId);
                if (targetOrder && targetOrder.driver_id) {
                    targetDriverId = targetOrder.driver_id;
                    console.log(`📍 从目标订单获取司机ID: ${targetDriverId}`);
                } else {
                    console.warn(`⚠️ 无法获取目标订单 ${targetInfo.targetOrderId} 的司机ID`);
                }
            }

            // 方法3：从目标路线的任意已分配订单获取司机ID（如果前两种方法都失败）
            if (!targetDriverId) {
                const targetRouteOrders = orderStore.getOrdersByRouteNumber(targetInfo.targetRouteId) || [];
                const assignedOrder = targetRouteOrders.find(o => o.driver_id);
                if (assignedOrder) {
                    targetDriverId = assignedOrder.driver_id;
                    console.log(`📍 从路线订单获取司机ID: ${targetDriverId}`);
                }
            }

            if (!targetDriverId) {
                console.error(`❌ 无法获取路线 ${targetInfo.targetRouteId} 的司机ID`);
                return false;
            }

            console.log(`🚀 本地分配 ${draggedOrders.length} 个未分配订单到路线 ${targetInfo.targetRouteId}, 司机 ${targetDriverId}`);

            // 获取目标路线的现有订单，用于计算新的 stop_no
            const targetRouteOrders = orderStore.getOrdersByRouteNumber(targetInfo.targetRouteId) || [];
            console.log(`📊 目标路线现有 ${targetRouteOrders.length} 个订单`);

            let insertAfterStopNo;
            let needsResequencing = false;

            // 根据拖拽类型确定插入位置
            if (targetInfo.type === 'unassigned_to_route_line') {
                // 拖拽到路线线上
                if (targetInfo.insertPosition === 'end') {
                    // 插入到路线末尾
                    const maxStopNo = targetRouteOrders.length > 0
                        ? Math.max(...targetRouteOrders.map(o => o.stop_no || 0))
                        : 0;
                    insertAfterStopNo = maxStopNo;
                    console.log(`📍 插入到路线末尾，当前最大 stop_no: ${maxStopNo}`);
                } else if (targetInfo.insertPosition === 'after_order') {
                    // 插入到指定订单之后
                    insertAfterStopNo = targetInfo.insertAfterStopNo;
                    needsResequencing = true;
                    console.log(`📍 插入到 stop ${insertAfterStopNo} 之后，需要重新编号后续订单`);
                }
            } else {
                // 拖拽到已分配订单标记上（原有逻辑）
                const targetOrder = targetRouteOrders.find(o => o.id === targetInfo.targetOrderId);
                if (!targetOrder) {
                    console.error(`❌ 目标订单 ${targetInfo.targetOrderId} 不在路线中`);
                    return false;
                }
                insertAfterStopNo = targetOrder.stop_no;
                needsResequencing = true;
                console.log(`🎯 插入到目标订单 ${targetInfo.targetOrderId} (stop ${insertAfterStopNo}) 之后`);
            }

            // 如果需要重新编号，先更新现有订单的 stop_no
            if (needsResequencing) {
                console.log(`🔄 重新编号 stop ${insertAfterStopNo} 之后的订单`);

                // 找到需要重新编号的订单（stop_no > insertAfterStopNo）
                const ordersToResequence = targetRouteOrders
                    .filter(o => o.stop_no > insertAfterStopNo)
                    .sort((a, b) => a.stop_no - b.stop_no);

                console.log(`📋 需要重新编号的订单:`, ordersToResequence.map(o => `${o.id}(${o.stop_no})`));

                // 为这些订单增加 stop_no（为新插入的订单腾出空间）
                ordersToResequence.forEach(order => {
                    const oldStopNo = order.stop_no;
                    order.stop_no = oldStopNo + draggedOrders.length;

                    // 同时更新 allOrders 中的对应订单
                    const allOrdersOrder = orderStore.allOrders.find(o => o.id === order.id);
                    if (allOrdersOrder) {
                        allOrdersOrder.stop_no = order.stop_no;
                    }

                    console.log(`📝 重新编号订单 ${order.id}: ${oldStopNo} → ${order.stop_no}`);
                });
            }

            // 本地更新每个拖拽的订单
            draggedOrders.forEach((order, index) => {
                const newStopNo = insertAfterStopNo + index + 1;

                console.log(`📝 更新订单 ${order.id}:`, {
                    route_id: `${order.route_id} → ${targetInfo.targetRouteId}`,
                    driver_id: `${order.driver_id} → ${targetDriverId}`,
                    stop_no: `${order.stop_no} → ${newStopNo}`,
                    status: `${order.status} → assigned`
                });

                // 更新订单属性
                order.route_id = targetInfo.targetRouteId;
                order.route_number = targetInfo.targetRouteId; // 保持兼容性
                order.driver_id = targetDriverId;
                order.stop_no = newStopNo;
                order.status = 'assigned';

                // 同时更新 orderStore.allOrders 中的对应订单对象
                const allOrdersIndex = orderStore.allOrders.findIndex(o => o.id === order.id);
                if (allOrdersIndex !== -1) {
                    const allOrdersOrder = orderStore.allOrders[allOrdersIndex];
                    allOrdersOrder.route_id = targetInfo.targetRouteId;
                    allOrdersOrder.route_number = targetInfo.targetRouteId;
                    allOrdersOrder.driver_id = targetDriverId;
                    allOrdersOrder.stop_no = newStopNo;
                    allOrdersOrder.status = 'assigned';
                    console.log(`📝 同步更新 allOrders 中的订单 ${order.id}`);
                }

                // 从未分配列表移到已分配列表
                const unassignedIndex = orderStore.orders.findIndex(o => o.id === order.id);
                if (unassignedIndex !== -1) {
                    const movedOrder = orderStore.orders.splice(unassignedIndex, 1)[0];
                    // 确保移动的订单也有最新的属性
                    movedOrder.route_id = targetInfo.targetRouteId;
                    movedOrder.route_number = targetInfo.targetRouteId;
                    movedOrder.driver_id = targetDriverId;
                    movedOrder.stop_no = newStopNo;
                    movedOrder.status = 'assigned';
                    orderStore.assignedOrders.push(movedOrder);
                    console.log(`📦 订单 ${order.id} 已从未分配列表移到已分配列表`);
                }
            });

            // 使缓存失效，强制重新计算
            orderStore.invalidateCache();

            // 发送事件通知UI更新（避免触发全局路线重绘）
            console.log('📡 发送 ORDERS_ASSIGNED 事件...');
            eventBus.emit(EVENT_TYPES.ORDERS_ASSIGNED, {
                orderIds: draggedOrders.map(o => o.id),
                routeId: targetInfo.targetRouteId,
                source: 'map-drag-local',
                skipGlobalRouteRedraw: true // 标记跳过全局路线重绘
            });

            // 强制刷新订单显示
            console.log('📡 强制刷新订单显示...');
            orderStore.refreshOrderDisplay();

            // 强制触发地图的全局订单刷新
            console.log('📡 触发地图全局订单刷新...');

            // 步骤2：只重新绘制更新的订单（高性能）
            console.log('📡 步骤2：重新绘制更新的订单...');

            // 获取更新后的订单数据
            const updatedOrderIds = draggedOrders.map(o => o.id);
            console.log('📡 需要重新绘制的订单ID:', updatedOrderIds);

            // 为每个更新的订单重新创建标记
            if (window.updateSingleOrderMarker && typeof window.updateSingleOrderMarker === 'function') {
                updatedOrderIds.forEach(orderId => {
                    const updatedOrder = orderStore.allOrders.find(o => o.id === orderId);
                    if (updatedOrder) {
                        console.log(`📡 重新绘制订单 ${orderId}:`, {
                            route_id: updatedOrder.route_id,
                            driver_id: updatedOrder.driver_id,
                            stop_no: updatedOrder.stop_no,
                            status: updatedOrder.status,
                            isAssigned: !!updatedOrder.driver_id
                        });
                        window.updateSingleOrderMarker(updatedOrder);
                    } else {
                        console.warn(`📡 找不到更新后的订单 ${orderId}`);
                    }
                });
            } else {
                console.warn('📡 updateSingleOrderMarker 函数不可用，回退到全量更新');
                // 回退方案：只更新当前显示的订单
                const ordersToDisplay = window.showAllOrders ? orderStore.allOrders : orderStore.orders;
                if (window.updateOrdersData && typeof window.updateOrdersData === 'function') {
                    window.updateOrdersData(ordersToDisplay);
                }
            }

            // 步骤3：延迟重新绘制真实路线（等待 OrderStore 更新完成）
            console.log('📡 步骤3：延迟重新绘制真实路线...');

            // 延迟执行路线重绘，确保 OrderStore 更新和所有事件处理完成
            setTimeout(() => {
                console.log('📡 开始延迟的路线重绘...');

                // 只重绘目标路线，不触发全局路线重绘
                if (window.mapRouteManagement && window.mapRouteManagement.drawRealisticRoute) {
                    console.log('🗺️ 未分配订单拖拽结束: 只重绘目标路线, routeId:', targetInfo.targetRouteId);
                    try {
                        // 获取路线信息
                        const routeStoreForRoute = window.routeStore;
                        const targetRoute = routeStoreForRoute?.getRouteById ? routeStoreForRoute.getRouteById(targetInfo.targetRouteId) : null;
                        const routeDriverId = targetRoute?.driver || targetRoute?.driverId || targetDriverId;

                        // 获取更新后的订单列表（此时应该已经包含新分配的订单）
                        const updatedRouteOrders = orderStore.getOrdersByRouteNumber(targetInfo.targetRouteId) || [];
                        console.log('📋 延迟重绘时的订单数量:', updatedRouteOrders.length);
                        console.log('📋 未分配订单拖拽使用新订单顺序:', updatedRouteOrders.map(o => ({ id: o.id, stop_no: o.stop_no, name: o.name })));

                        // 只重绘这一条路线，不影响其他路线
                        window.mapRouteManagement.drawRealisticRoute(targetInfo.targetRouteId, routeDriverId, updatedRouteOrders);
                        console.log('✅ 未分配订单拖拽真实路线重绘已触发（仅目标路线）');
                    } catch (error) {
                        console.error('❌ 未分配订单拖拽重新绘制真实路线失败:', error);
                    }
                } else {
                    console.warn('⚠️ mapRouteManagement 不可用，无法重新绘制真实路线');
                }
            }, 500); // 延迟500ms，确保所有更新完成

            // 发送订单分配事件（避免触发全局路线重绘）
            eventBus.emit(EVENT_TYPES.ORDERS_ASSIGNED, {
                orderIds: updatedOrderIds,
                routeId: targetInfo.targetRouteId,
                source: 'map-drag-local',
                skipGlobalRouteRedraw: true // 标记跳过全局路线重绘
            });

            // 标记路线有未保存的更改
            if (routeStore && routeStore.markRouteAsModified) {
                routeStore.markRouteAsModified(targetInfo.targetRouteId);
                console.log(`🔄 标记路线 ${targetInfo.targetRouteId} 有未保存的更改`);
            }

            console.log(`✅ 成功本地分配 ${draggedOrders.length} 个订单到路线 ${targetInfo.targetRouteId}`);
            return true;

        } catch (error) {
            console.error('❌ 处理未分配订单本地分配时发生错误:', error);
            return false;
        }
    }

    // 清理拖拽状态
    const cleanupDragState = () => {
        // 移除幽灵标记
        if (ghostMarker.value) {
            if (ghostMarker.value.remove) {
                ghostMarker.value.remove();
            } else if (ghostMarker.value.parentNode) {
                ghostMarker.value.parentNode.removeChild(ghostMarker.value);
            }
            ghostMarker.value = null;
        }

        // 清除路线高亮
        clearRouteLineHighlight();

        // 重置高亮目标
        highlightedTargetId.value = null;

        // 重新启用地图拖拽功能
        if (map.value) {
            map.value.dragPan.enable();
        }

        // 重置拖拽状态
        isDraggingMapSymbol.value = false;
        draggedOrderInfo.value = null;
        affectedRouteId.value = null;
        isMultiDragMap.value = false;
        multiDragMapOrderIds.value = [];

        // 重置新增的拖拽类型状态
        dragType.value = null;
        isUnassignedDrag.value = false;
    }

    // 处理订单更新事件
    const handleOrdersUpdated = (data) => {
        if (data.source === 'map-drag') return // 避免循环更新

        // 如果正在拖拽，不处理更新
        if (isDraggingMapSymbol.value) return

        // 确保 Symbol 图层在最顶层
        if (moveSymbolLayersToTop) {
            moveSymbolLayersToTop()
        }

        // 转发事件到 MapViewNew.vue 中的 handleOrdersUpdated 函数
        // 这样可以确保 MapViewNew.vue 中的 handleOrdersUpdated 函数能够处理 isStatusChange 标志的情况
        eventBus.emit('internal:orders-updated', data);
    }

    // 处理混合状态聚合标记拖拽（既有已分配又有未分配订单）
    const handleMixedClusterDrag = (clusterOrderIds) => {
        const orderStore = useOrderStore()

        // 分析聚合中的订单状态
        const allOrders = clusterOrderIds
            .map(id => orderStore.allOrders.find(o => o.id === id))
            .filter(o => o)

        const assignedOrders = allOrders.filter(o => o.route_id)
        const unassignedOrders = allOrders.filter(o => !o.route_id)

        console.log(`聚合标记分析: 总计${allOrders.length}单, 已分配${assignedOrders.length}单, 未分配${unassignedOrders.length}单`)

        // 如果只有一种类型的订单，使用原有逻辑
        if (assignedOrders.length === 0) {
            return { type: 'unassigned_only', orders: unassignedOrders }
        }
        if (unassignedOrders.length === 0) {
            return { type: 'assigned_only', orders: assignedOrders }
        }

        // 混合状态：提供选择
        return {
            type: 'mixed',
            assignedOrders,
            unassignedOrders,
            allOrders
        }
    }

    // 根据用户选择开始聚合拖拽
    const startClusterDragWithSelection = (selection, originalEvent) => {
        const ordersToMove = selection.orders
        const firstOrder = ordersToMove[0]

        // 设置拖拽状态
        isDraggingMapSymbol.value = true

        // 判断拖拽类型
        if (firstOrder.route_id) {
            dragType.value = 'assigned'
            isUnassignedDrag.value = false
            affectedRouteId.value = firstOrder.route_id
            console.log(`开始拖拽已分配订单聚合 (${ordersToMove.length}单)`)
        } else {
            dragType.value = 'unassigned'
            isUnassignedDrag.value = true
            affectedRouteId.value = null
            console.log(`开始拖拽未分配订单聚合 (${ordersToMove.length}单)`)
        }

        // 设置多拖拽状态
        isMultiDragMap.value = ordersToMove.length > 1
        multiDragMapOrderIds.value = ordersToMove.map(o => o.id)

        // 记录原始位置信息
        const lngLat = originalEvent.lngLat
        const screenPoint = map.value.project(lngLat)

        // 设置拖拽信息
        draggedOrderInfo.value = {
            orderId: firstOrder.id,
            routeId: firstOrder.route_id,
            originalLngLat: [lngLat.lng, lngLat.lat],
            originalScreenPoint: [screenPoint.x, screenPoint.y],
            selectedOrderIds: ordersToMove.map(o => o.id) // 添加选中的订单ID列表
        }

        // 禁用地图拖拽功能
        if (map.value) {
            map.value.dragPan.disable()
        }

        // 发送事件通知其他组件拖拽开始
        eventBus.emit(EVENT_TYPES.DRAG_START, {
            orderId: firstOrder.id,
            routeId: firstOrder.route_id,
            selectedOrderIds: ordersToMove.map(o => o.id),
            timestamp: Date.now()
        })

        // 创建拖拽视觉反馈
        createGhostMarker(lngLat, firstOrder)

        // 高亮被拖拽的订单标记
        highlightDraggedSymbols()

        console.log(`聚合拖拽已开始，拖拽订单:`, ordersToMove.map(o => o.id))
    }

    // 导出一个计算属性，用于其他组件检查是否正在拖拽
    const isCurrentlyDragging = computed(() => isDraggingMapSymbol.value)

    return {
        // 状态
        isDraggingMapSymbol,
        isCurrentlyDragging, // 添加计算属性，方便其他组件使用
        draggedOrderInfo,
        affectedRouteId,
        isMultiDragMap,
        multiDragMapOrderIds,

        // 方法
        initDragHandlers,
        cleanupDragHandlers,
        handleSymbolMouseDown,
        handleSymbolDrag,
        handleSymbolMouseUp,
        handleSymbolTouchStart,
        handleSymbolTouchMove,
        handleSymbolTouchEnd,
        handleOrdersUpdated,

        // 辅助方法
        createGhostMarker,
        updateGhostMarkerPosition,
        highlightDraggedSymbols,
        highlightDropTarget,
        findPotentialDropTarget,
        determineDropTargetIndex,
        calculateNewOrderSequence,
        cleanupDragState,

        // 混合状态拖拽处理
        handleMixedClusterDrag,
        startClusterDragWithSelection,

        // 路线线拖拽处理
        getRouteLayerIds,
        handleRouteLineDropTarget,
        handleUnassignedToMarkerDrop,
        handleUnassignedToRouteLineDrop,
        findNearestOrderOnRoute,
        highlightRouteLine,
        clearRouteLineHighlight
    }
}
