import { defineStore } from 'pinia'
import { useDriverStore } from './driver'
import { ref } from 'vue'
import { useRouteStore } from './route'
import { useTimeStore } from '../stores/time'
import { OrderService } from '../services/OrderService'

export const useOrderStore = defineStore('order', {
    state: () => ({
        isLoading: false,
        currentOrderType: ref('PICKUP'),
        // 创建OrderService实例
        orderService: new OrderService(),
        orders: [],
        allOrders: [],
        selectedOrder: null,
        error: null,
        lastFetch: null,
        orderCache: {},
        routesWithUnsavedChanges: new Set(), // 存储有未保存更改的路线ID
        // 防重复调用相关状态
        lastFetchKey: null,
        lastFetchTime: 0
    }),

    getters: {
    // 获取所有订单
        allOrders: state => {
            return state.orderService.getAllOrders()
        },

        // 按类型筛选订单
        ordersByType: state => type => {
            return state.orderService.getOrdersByType(type)
        },

        // 获取指定司机的订单
        getDriverOrders: state => driverId => {
            return state.orderService.getDriverOrders(driverId)
        },

        selectedOrderIds: state => {
            return state.orderService.selectedOrderIds
        },

        selectedOrders: state => {
            return state.orderService.selectedOrders
        },

        // 获取指定路线的订单
        getOrdersByRouteNumber: state => routeNumber => {
            const routeOrders = state.orderService.getOrdersByRouteNumber(routeNumber)
            console.log(`[OrderStore] 通过路线号 ${routeNumber} 获取订单结果:`, routeOrders?.length || 0)

            // 如果没有找到订单，尝试通过其他方式查找
            if (!routeOrders || routeOrders.length === 0) {
                // 检查路线ID是否是路线名称
                const routeStore = useRouteStore();
                const route = routeStore.routes.find(r => r.name === routeNumber || r.id === routeNumber);

                if (route) {
                    console.log(`[OrderStore] 找到路线: ID=${route.id}, 名称=${route.name}, 司机=${route.driver || route.driverId}`);

                    // 尝试通过路线ID查找订单
                    const routeIdOrders = state.orderService.getAllOrders().filter(order =>
                        order.route_id === route.id ||
                        order.route_number === route.id ||
                        order.route_id === route.name ||
                        order.route_number === route.name
                    );

                    if (routeIdOrders.length > 0) {
                        console.log(`[OrderStore] 通过路线ID/名称匹配找到 ${routeIdOrders.length} 个订单`);
                        return routeIdOrders;
                    }

                    // 如果仍未找到订单，尝试通过司机ID查找
                    if (route.driver || route.driverId) {
                        const driverId = route.driver || route.driverId;
                        const driverOrders = state.orderService.getDriverOrders(driverId);

                        if (driverOrders && driverOrders.length > 0) {
                            console.log(`[OrderStore] 通过司机ID找到 ${driverOrders.length} 个订单，尝试关联到路线`);

                            // 更新这些订单的路线ID字段
                            driverOrders.forEach(order => {
                                if (!order.route_id) {
                                    order.route_id = route.id;
                                    order.route_number = route.name;
                                    console.log(`[OrderStore] 将订单 ${order.id} 关联到路线 ${route.id}`);
                                }
                            });

                            // 使缓存失效
                            state.orderService.invalidateCache();

                            // 将这些订单添加到路线中
                            return driverOrders;
                        }
                    }

                    console.log(`[OrderStore] 无法找到任何与路线 ${routeNumber} 关联的订单`);
                }
            }

            return routeOrders
        },

        // 获取未分配订单
        orders: state => {
            return state.orderService.orders
        },

        // 获取已分配订单
        assignedOrders: state => {
            return state.orderService.assignedOrders
        },

        // 判断选中的订单中是否有已分配的订单
        hasAssignedOrders: state => {
            if (!state.orderService.selectedOrders.length) return false
            return state.orderService.selectedOrders.some(order => order.driver_id || order.route_number)
        }
    },

    actions: {
        // 本地更新订单顺序（不保存到服务器）
        updateLocalOrderSequence(routeId, updatedOrders) {
            console.log(`[OrderStore] 本地更新路线 ${routeId} 的订单顺序`);

            if (!updatedOrders || !Array.isArray(updatedOrders) || updatedOrders.length === 0) {
                console.error('[OrderStore] updateLocalOrderSequence: 无效的订单数据');
                return false;
            }

            // 更新本地状态
            updatedOrders.forEach(order => {
                // 查找订单在allOrders中的索引
                const orderIndex = this.orderService.assignedOrders.findIndex(o => o.id === order.id);
                if (orderIndex >= 0) {
                    // 更新stop_no
                    this.orderService.assignedOrders[orderIndex].stop_no = order.stop_no;
                    console.log(`[OrderStore] 更新订单 ${order.id} 的停靠点编号为 ${order.stop_no}`);
                }
            });

            // 标记路线有未保存的更改
            this.routesWithUnsavedChanges.add(routeId);

            // 使缓存失效
            this.invalidateCache();

            return true;
        },

        // 检查路线是否有未保存的更改
        hasUnsavedChanges(routeId) {
            return this.routesWithUnsavedChanges.has(routeId);
        },

        // 清除路线的未保存更改标记
        clearUnsavedChanges(routeId) {
            if (this.routesWithUnsavedChanges.has(routeId)) {
                this.routesWithUnsavedChanges.delete(routeId);
                console.log(`[OrderStore] 清除路线 ${routeId} 的未保存更改标记`);
            }
        },

        // 更新订单的路段信息（距离和时间）
        updateOrderSegmentInfo(orderId, segmentDistance, segmentDuration) {
            console.log(`[OrderStore] 更新订单 ${orderId} 的路段信息: 距离=${segmentDistance}m, 时间=${segmentDuration}s`);

            // 查找订单在哪个列表中
            let foundOrder = null;

            // 先在已分配订单中查找
            foundOrder = this.orderService.assignedOrders.find(o => o.id === orderId);

            // 如果没找到，在未分配订单中查找
            if (!foundOrder) {
                foundOrder = this.orderService.orders.find(o => o.id === orderId);
            }

            if (foundOrder) {
                // 更新路段信息，保持原始单位（米和秒）
                foundOrder.segmentDistance = segmentDistance;
                foundOrder.segmentDuration = segmentDuration;

                console.log(`[OrderStore] 成功更新订单 ${orderId} 的路段信息`);

                // 使缓存失效
                this.invalidateCache();
                return true;
            } else {
                console.warn(`[OrderStore] 未找到订单 ${orderId}，无法更新路段信息`);
                return false;
            }
        },

        // 批量更新多个订单的路段信息
        updateOrdersSegmentInfo(orderSegmentData) {
            console.log(`[OrderStore] 批量更新 ${orderSegmentData.length} 个订单的路段信息`);

            let updatedCount = 0;

            orderSegmentData.forEach(({ orderId, segmentDistance, segmentDuration }) => {
                if (this.updateOrderSegmentInfo(orderId, segmentDistance, segmentDuration)) {
                    updatedCount++;
                }
            });

            console.log(`[OrderStore] 成功更新 ${updatedCount}/${orderSegmentData.length} 个订单的路段信息`);
            return updatedCount;
        },

        // 获取订单的路段信息
        getOrderSegmentInfo(orderId) {
            // 查找订单
            let foundOrder = null;

            // 先在已分配订单中查找
            foundOrder = this.orderService.assignedOrders.find(o => o.id === orderId);

            // 如果没找到，在未分配订单中查找
            if (!foundOrder) {
                foundOrder = this.orderService.orders.find(o => o.id === orderId);
            }

            if (foundOrder && foundOrder.segmentDistance !== undefined && foundOrder.segmentDuration !== undefined) {
                return {
                    distance: foundOrder.segmentDistance, // 米
                    duration: foundOrder.segmentDuration, // 秒
                    orderId: orderId
                };
            }

            return null;
        },

    // 获取所有订单
        async fetchOrders() {
            console.trace("fetchOrders triggered by:");
            const timeStore = useTimeStore()

            // 防重复调用机制
            const queryKey = `${timeStore.selectedDate}-${timeStore.selectedShift?.id || 'no-shift'}`;
            const now = Date.now();

            // 如果相同参数的请求在1秒内已经发起过，则跳过
            if (this.lastFetchKey === queryKey && (now - this.lastFetchTime) < 1000) {
                console.log(`⚠️ 防重复调用：跳过重复的 fetchOrders 请求 (${queryKey})`);
                return;
            }

            // 如果正在加载中，则跳过
            if (this.isLoading) {
                console.log(`⚠️ 防重复调用：fetchOrders 正在进行中，跳过新请求`);
                return;
            }

            this.lastFetchKey = queryKey;
            this.lastFetchTime = now;
            this.isLoading = true

            try {
                // 在 store 层构建查询参数
                const queryParams = {
                    date: timeStore.selectedDate
                }

                // 添加 shiftId 参数
                if (timeStore.selectedShift?.id) {
                    queryParams.shiftId = timeStore.selectedShift.id;
                    console.log(`✅ 使用 shiftId=${queryParams.shiftId} 获取订单`);
                } else {
                    console.warn('fetchOrders: 无效的班次ID，无法获取订单。当前班次:', timeStore.selectedShift);
                    // 可以选择直接返回空，或者抛出错误，或者获取当天所有订单等
                    this.isLoading = false;
                    return; // 或者 throw new Error('...');
                }

                console.log('✅ fetchOrders 使用的查询参数:', queryParams);

                // 使用服务获取订单
                await this.orderService.fetchOrders(queryParams)

                // 统计 pickup 和 delivery 单数
                const allOrders = this.orderService.getAllOrders()
                const pickupCount = allOrders.filter(order => order.type === 'PICKUP').length
                const deliveryCount = allOrders.filter(order => order.type === 'DELIVERY').length

                console.log('订单统计:', {
                    pickup: pickupCount,
                    delivery: deliveryCount,
                    total: allOrders.length
                })

                this.isLoading = false
            } catch (error) {
                console.error('获取订单失败:', error)
                this.isLoading = false
                throw error
            }
        },

        // *** 添加单个订单到状态 ***
        addSingleOrder(orderData) {
            if (!orderData || !orderData.id) {
                console.error('[OrderStore] addSingleOrder: 无效的订单数据', orderData);
                return false;
            }
            console.log(`[Vue Reactivity] [OrderStore] addSingleOrder: 尝试添加订单 ${orderData.id}`);

            // 检查订单是否已存在
            const existingUnassigned = this.orderService.orders.find(o => o.id === orderData.id);
            const existingAssigned = this.orderService.assignedOrders.find(o => o.id === orderData.id);

            if (existingUnassigned || existingAssigned) {
                console.warn(`[Vue Reactivity] [OrderStore] 订单 ${orderData.id} 已存在，将尝试更新而不是添加。`);

                // 更新现有订单
                if (existingUnassigned) {
                    console.log(`[Vue Reactivity] [OrderStore] 更新未分配列表中的订单 ${orderData.id}`);
                    Object.assign(existingUnassigned, orderData);
                } else {
                    console.log(`[Vue Reactivity] [OrderStore] 更新已分配列表中的订单 ${orderData.id}`);
                    Object.assign(existingAssigned, orderData);
                }

                this.invalidateCache(); // 使依赖 getAllOrders 的缓存失效
                return true;
            }

            // 判断订单是未分配还是已分配
            if (orderData.route_id || orderData.driver_id) {
                // 添加到已分配订单列表
                console.log(`[Vue Reactivity] [OrderStore] 将新订单 ${orderData.id} 添加到已分配列表`);
                this.orderService.assignedOrders.push(orderData);
            } else {
                // 添加到未分配订单列表
                console.log(`[Vue Reactivity] [OrderStore] 将新订单 ${orderData.id} 添加到未分配列表`);
                this.orderService.orders.push(orderData);
            }

            console.log(`[Vue Reactivity] [OrderStore] 成功添加订单 ${orderData.id}，当前未分配订单数量: ${this.orderService.orders.length}，已分配订单数量: ${this.orderService.assignedOrders.length}`);

            this.invalidateCache(); // 使依赖 getAllOrders 的缓存失效

            // 触发订单列表更新事件，传递新订单ID
            this.orderService.refreshOrderDisplay([orderData.id]);

            // 触发订单添加事件
            eventBus.emit(EVENT_TYPES.ORDER_ADDED, {
                order: orderData,
                timestamp: Date.now()
            });

            return true;
        },

        // *** 新增方法：仅更新订单状态 ***
        updateOrderStatus(orderId, newStatus) {
            if (!orderId || newStatus === undefined) {
                console.error('[OrderStore] updateOrderStatus: 无效的参数', { orderId, newStatus });
                return false;
            }
            console.log(`[OrderStore] updateOrderStatus: 尝试更新订单 ${orderId} 状态为 ${newStatus}`);

            let updated = false;
            let foundOrder = null;

            // 查找订单在哪个列表
            const unassignedIndex = this.orderService.orders.findIndex(o => o.id === orderId);
            const assignedIndex = this.orderService.assignedOrders.findIndex(o => o.id === orderId);

            if (unassignedIndex !== -1) {
                foundOrder = this.orderService.orders[unassignedIndex];
            } else if (assignedIndex !== -1) {
                foundOrder = this.orderService.assignedOrders[assignedIndex];
            }

            if (foundOrder) {
                if (foundOrder.status !== newStatus) {
                    console.log(`[OrderStore] 订单 ${orderId} 状态从 ${foundOrder.status} 更新为 ${newStatus}`);
                    foundOrder.status = newStatus;
                    updated = true;
                    this.invalidateCache(); // 使缓存失效
                } else {
                    console.log(`[OrderStore] 订单 ${orderId} 状态已经是 ${newStatus}，无需更新`);
                }
            } else {
                console.warn(`[OrderStore] updateOrderStatus: 未找到订单 ${orderId}`);
                return false; // 未找到订单
            }

            return updated; // 返回是否成功更新
        },

        // *** 重构方法：从API获取完整订单信息并更新 ***
        async updateSingleOrder(orderId) {
            if (!orderId) {
                console.error('[OrderStore] updateSingleOrder (from API): 无效的订单ID');
                return false;
            }
            console.log(`[OrderStore] updateSingleOrder (from API): 尝试通过API更新订单 ${orderId}`);

            // 1. 查找本地订单的当前位置和索引 (如果需要先检查是否存在或获取类型)
            let currentList = null;
            let currentIndex = -1;
            let isCurrentlyAssigned = false;
            let localOrder = null; // 用于存储找到的本地订单对象

            currentIndex = this.orderService.assignedOrders.findIndex(o => o.id === orderId);
            if (currentIndex !== -1) {
                currentList = this.orderService.assignedOrders;
                isCurrentlyAssigned = true;
                console.log(`[OrderStore] 订单 ${orderId} 当前在已分配列表`);
                localOrder = currentList[currentIndex]; // 获取本地订单对象
            } else {
                currentIndex = this.orderService.orders.findIndex(o => o.id === orderId);
                if (currentIndex !== -1) {
                    currentList = this.orderService.orders;
                    localOrder = currentList[currentIndex]; // 获取本地订单对象
                    isCurrentlyAssigned = false;
                    console.log(`[OrderStore] 订单 ${orderId} 当前在未分配列表`);
                } else {
                    console.warn(`[OrderStore] updateSingleOrder (from API): 本地未找到订单 ${orderId}，无法继续API更新`);
                    // 可以选择在这里尝试添加，但通常更新是针对已存在的
                    return false;
                }
            }

            // 2. 调用API获取最新完整订单数据
            let fullOrderData;
            try {
                // **修改点：** 获取本地订单类型并调用新的Service方法
                if (!localOrder || !localOrder.type) {
                    console.error(`[OrderStore] updateSingleOrder (from API): 本地订单 ${orderId} 缺少 'type' 属性，无法调用 API`);
                    return false;
                }
                const orderType = localOrder.type;
                console.log(`[OrderStore] 使用本地订单类型 '${orderType}' 调用 API`);

                // 调用新的 Service 方法
                fullOrderData = await this.orderService.fetchOrderByIdAndType(orderId, orderType);
                if (!fullOrderData) {
                    // Service 层应该已经处理了 null 的情况，但这里可以再加一层保险
                    throw new Error(`API未返回订单 ${orderId} (Type: ${orderType}) 的数据`);
                }
                console.log(`[OrderStore] 从API获取到订单 ${orderId} 的完整数据`);
            } catch (error) {
                console.error(`[OrderStore] updateSingleOrder (from API): 调用API获取订单 ${orderId} 失败:`, error);
                // 根据需要处理错误，例如重试或通知用户
                return false;
            }

            // 3. 确定更新后的数据应该属于哪个列表
            const shouldBeAssigned = !!fullOrderData.route_id || !!fullOrderData.driver_id;
            console.log(`[OrderStore] 根据API数据，订单 ${orderId} ${shouldBeAssigned ? '应该属于已分配' : '应该属于未分配'}`);

            let updated = false;
            let moved = false;

            // 4. 更新或移动订单
            if (shouldBeAssigned === isCurrentlyAssigned) {
                // 订单列表归属不变，直接替换
                console.log(`[OrderStore] 订单 ${orderId} 列表归属不变，替换对象`);
                currentList[currentIndex] = fullOrderData;
                updated = true;
            } else {
                // 订单列表归属改变，需要移动
                console.log(`[OrderStore] 订单 ${orderId} 列表归属改变，执行移动`);
                // a. 从当前列表移除
                currentList.splice(currentIndex, 1);
                // b. 添加到目标列表
                const targetList = shouldBeAssigned ? this.orderService.assignedOrders : this.orderService.orders;
                targetList.push(fullOrderData);
                moved = true;
                // 可以在这里添加排序逻辑 targetList.sort(...)
            }

            if (updated || moved) {
                this.invalidateCache(); // 使缓存失效
            }

            return updated || moved; // 返回是否有变化
        },

        // 添加已分配订单
        async addAssignedOrders(orders) {
            return this.orderService.addAssignedOrders(orders)
        },

        // 分配订单给司机
        async assignOrder(orderId, driverId, existingRouteNumber = null) {
            return this.orderService.assignOrder(orderId, driverId, existingRouteNumber)
        },

        // 分配多个订单给司机
        async assignOrdersToDriver(orderIds, driverId, existingRouteNumber = null) {
            return this.orderService.assignOrdersToDriver(orderIds, driverId, existingRouteNumber)
        },

        // 使用API分配订单到路线
        async assignOrdersToRouteWithAPI(orders, routeId, driverId = null, autoOptimize = false) {
            try {
                // 从传入的 orders 对象中获取 orderIds
                const orderIds = orders.map(o => o.id);

                // 在API调用之前，先直接更新本地状态
                // 如果提供了司机ID，直接使用；否则尝试从路线获取
                if (driverId) {
                    console.log(`[OrderStore] 使用提供的司机ID: ${driverId} 更新订单`);
                    this.updateLocalOrdersWithDriverId(orderIds, routeId, driverId);
                } else {
                    console.log(`[OrderStore] 未提供司机ID，尝试从路线获取`);
                    this.updateLocalOrdersAfterAssign(orderIds, routeId);
                }

                // 根据 autoOptimize 参数决定使用哪个方法
                let result;
                if (autoOptimize) {
                    console.log(`[OrderStore] 使用自动优化模式分配订单到路线 ${routeId}`);
                    result = await this.orderService.assignOrdersToRouteWithOptimization(orders, routeId, driverId);
                } else {
                    console.log(`[OrderStore] 使用普通模式分配订单到路线 ${routeId}`);
                    result = await this.orderService.assignOrdersToRouteWithAPI(orders, routeId);
                }

                // 成功后强制刷新UI
                this.refreshOrderDisplay();

                return result;
            } catch (error) {
                console.error('分配订单失败，回滚本地状态:', error);
                // 发生错误时可能需要重新获取数据
                this.invalidateCache();
                throw error;
            }
        },

        // 使用TomTom优化路线顺序并分配订单
        async assignOrdersWithOptimization(orders, routeId, driverId = null) {
            try {
                console.log(`[OrderStore] 开始优化分配 ${orders.length} 个订单到路线 ${routeId}`);

                // 在API调用之前，先直接更新本地状态
                const orderIds = orders.map(o => o.id);
                if (driverId) {
                    console.log(`[OrderStore] 使用提供的司机ID: ${driverId} 更新本地状态`);
                    this.updateLocalOrdersWithDriverId(orderIds, routeId, driverId);
                } else {
                    console.log(`[OrderStore] 未提供司机ID，尝试从路线获取后更新本地状态`);
                    this.updateLocalOrdersAfterAssign(orderIds, routeId);
                }

                // 执行分配和优化
                const result = await this.orderService.assignOrdersToRouteWithOptimization(
                    orders,
                    routeId,
                    driverId
                );

                // 成功后强制刷新UI
                this.refreshOrderDisplay();

                return result;
            } catch (error) {
                console.error('[OrderStore] 优化分配订单失败:', error);
                this.invalidateCache();
                throw error;
            }
        },

        // 对已分配订单进行路线优化（不重新分配）
        async optimizeRouteSequence(routeId, driverId) {
            try {
                console.log(`[OrderStore] 优化路线 ${routeId} 的订单顺序，传入的司机ID: ${driverId}`);

                // 获取路线对象，确保我们有正确的route.id和driver_id
                const routeStore = useRouteStore();
                let actualRouteId = routeId;
                let actualDriverId = driverId;

                // 检查传入的routeId是否是路线名称而不是实际ID
                const route = routeStore.routes.find(r => r.name === routeId || r.id === routeId);
                if (route) {
                    console.log(`[OrderStore] 找到路线: ID=${route.id}, 名称=${route.name}, 司机=${route.driver || route.driverId}`);

                    // 使用实际的路线ID
                    actualRouteId = route.id;

                    // 如果没有传入司机ID，使用路线对象中的司机ID
                    if (!actualDriverId && (route.driver || route.driverId)) {
                        actualDriverId = route.driver || route.driverId;
                        console.log(`[OrderStore] 从路线获取司机ID: ${actualDriverId}`);
                    }
                } else {
                    console.warn(`[OrderStore] 无法找到匹配的路线: ${routeId}`);
                }

                // 获取路线上的所有订单
                let routeOrders = this.getOrdersByRouteNumber(routeId);

                // 如果没有找到路线订单但有司机ID，尝试获取司机的所有订单
                if ((!routeOrders || routeOrders.length === 0) && actualDriverId) {
                    console.log(`[OrderStore] 路线 ${routeId} 没有关联订单，尝试使用司机 ${actualDriverId} 的全部订单`);
                    routeOrders = this.getDriverOrders(actualDriverId);

                    if (routeOrders && routeOrders.length > 0) {
                        console.log(`[OrderStore] 使用司机 ${actualDriverId} 的 ${routeOrders.length} 个订单进行优化`);

                        // 将这些订单关联到路线
                        if (actualRouteId) {
                            console.log(`[OrderStore] 将司机订单关联到路线 ${actualRouteId}`);
                            routeOrders.forEach(order => {
                                if (!order.route_id) {
                                    order.route_id = actualRouteId;
                                    order.route_number = routeId;
                                }
                            });

                            // 使缓存失效
                            this.invalidateCache();
                        }
                    }
                }

                if (!routeOrders || routeOrders.length === 0) {
                    console.warn(`[OrderStore] 路线 ${routeId} 没有订单，无法优化`);
                    return [];
                }

                console.log(`[OrderStore] 确认使用参数: 路线ID=${actualRouteId}, 司机ID=${actualDriverId} 进行优化，共 ${routeOrders.length} 个订单`);

                // 调用Service层的优化方法
                const optimizedOrders = await this.orderService.optimizeRouteOrderSequence(
                    routeOrders,
                    actualDriverId,
                    actualRouteId
                );

                // 更新停靠点编号
                const updatedOrders = await this.orderService.updateOrdersStopNumbers(
                    optimizedOrders,
                    actualRouteId,
                    optimizedOrders[0]?.type
                );

                // 刷新UI显示
                this.refreshOrderDisplay();

                return updatedOrders;
            } catch (error) {
                console.error('[OrderStore] 优化路线顺序失败:', error);
                return [];
            }
        },

        // 重新排序路线停靠点
        async reorderRouteStops(routeId, orderedOrderIds) {
            try {
                console.log(`[OrderStore] 重新排序路线 ${routeId} 的停靠点，共 ${orderedOrderIds.length} 个订单`);

                // 获取路线对象
                const route = await this.orderService.routeRepository.getRouteById(routeId);
                if (!route) {
                    console.warn(`[OrderStore] 找不到路线ID: ${routeId}`);
                    return [];
                }

                // 获取路线名称
                const routeName = route.name || routeId;
                console.log(`[OrderStore] 路线ID ${routeId} 对应的路线名称: ${routeName}`);

                // 获取路线上的所有订单 - 首先尝试通过route_id获取
                let routeOrders = this.allOrders.filter(order => order.route_id === routeId);

                // 如果通过route_id找不到，尝试通过路线名称获取
                if (!routeOrders || routeOrders.length === 0) {
                    routeOrders = this.getOrdersByRouteNumber(routeName);
                    console.log(`[OrderStore] 通过路线名称获取订单: ${routeOrders?.length || 0}个`);
                } else {
                    console.log(`[OrderStore] 通过路线ID获取订单: ${routeOrders.length}个`);
                }

                if (!routeOrders || routeOrders.length === 0) {
                    console.warn(`[OrderStore] 路线 ${routeId} 没有订单，无法重新排序`);
                    return [];
                }

                // 创建ID到订单对象的映射
                const orderMap = {};
                routeOrders.forEach(order => {
                    orderMap[order.id] = order;
                });

                // 按照指定的顺序重新排列订单
                const reorderedOrders = [];
                orderedOrderIds.forEach(id => {
                    if (orderMap[id]) {
                        reorderedOrders.push(orderMap[id]);
                    } else {
                        console.warn(`[OrderStore] 订单ID ${id} 不在路线 ${routeId} 中，跳过`);
                    }
                });

                if (reorderedOrders.length === 0) {
                    console.warn(`[OrderStore] 重新排序后没有有效订单，取消操作`);
                    return [];
                }

                console.log(`[OrderStore] 重新排序后有 ${reorderedOrders.length} 个有效订单`);

                // 更新停靠点编号
                const updatedOrders = await this.orderService.updateOrdersStopNumbers(
                    reorderedOrders,
                    routeId,
                    reorderedOrders[0]?.type
                );

                // 清除未保存更改标记
                this.clearUnsavedChanges(routeId);

                // 刷新UI显示
                this.refreshOrderDisplay();

                return updatedOrders;
            } catch (error) {
                console.error('[OrderStore] 重新排序路线停靠点失败:', error);
                return [];
            }
        },

        // 取消分配订单（将driverId设为null，routeId设为null）
        async unassignOrders(orderIds) {
            const results = []
            for (const orderId of orderIds) {
                // 传入null作为driverId和routeId，表示取消分配
                const result = await this.orderService.assignOrder(orderId, null, null)
                if (result) {
                    results.push(result)
                }
            }
            return results
        },

        // 使用API取消分配订单
        async unassignOrdersWithAPI(orderIds) {
            try {
                // 在API调用之前，先直接更新本地状态
                this.updateLocalOrdersAfterUnassign(orderIds);

                // 然后执行API请求
                const result = await this.orderService.unassignOrdersWithAPI(orderIds);

                // 成功后强制刷新UI
                this.refreshOrderDisplay();

                return result;
            } catch (error) {
                console.error('取消分配订单失败，回滚本地状态:', error);
                // 发生错误时可能需要重新获取数据
                this.invalidateCache();
                throw error;
            }
        },

        // 选择订单
        selectOrder(order) {
            return this.orderService.selectOrder(order)
        },

        // 清除选中的订单
        clearSelectedOrder() {
            this.orderService.clearSelectedOrder()
        },

        // 选择/取消选择订单
        toggleOrderSelection(order) {
            return this.orderService.toggleOrderSelection(order)
        },

        // 批量选择订单
        selectOrders(orderIds) {
            return this.orderService.selectOrders(orderIds)
        },

        // 设置选中的订单ID集合
        setSelectedOrderIds(orderIds) {
            // 调用service的selectOrders方法，实现相同的功能
            return this.orderService.selectOrders(orderIds)
        },

        // 清除所有选择
        clearSelection() {
            this.orderService.clearSelection()
        },

        // 清除已分配订单
        clearAssignedOrders() {
            this.orderService.clearAssignedOrders()
        },

        // 清除已选中的订单
        clearSelectedOrders() {
            this.orderService.clearSelection()
        },

        // 清除缓存
        invalidateCache() {
            console.log('清除订单缓存')
            this.orderCache = {}
            this.lastFetch = null
        },

        // 强制刷新订单显示
        refreshOrderDisplay() {
            return this.orderService.refreshOrderDisplay()
        },

        // 更新订单状态（用于Firebase消息处理）
        updateOrderStatus(orderId, newStatus) {
            console.log(`[OrderStore] 更新订单 ${orderId} 状态为 ${newStatus}`);

            // 查找订单（先在已分配订单中查找，再在未分配订单中查找）
            let order = this.orderService.assignedOrders.find(o => o.id === orderId);
            if (!order) {
                order = this.orderService.orders.find(o => o.id === orderId);
            }

            if (!order) {
                console.warn(`[OrderStore] 未找到订单 ${orderId}，无法更新状态`);
                return false;
            }

            // 检查状态是否已经是最新的
            if (order.status === newStatus) {
                console.log(`[OrderStore] 订单 ${orderId} 状态已经是 ${newStatus}，无需更新`);
                return false;
            }

            // 更新状态
            order.status = newStatus;
            console.log(`[OrderStore] 订单 ${orderId} 状态已更新为 ${newStatus}`);

            // 使缓存失效
            this.invalidateCache();

            return true;
        },



        // 更新单个订单（用于Firebase消息处理）
        async updateSingleOrder(orderId) {
            console.log(`[OrderStore] 更新单个订单: ${orderId}`);

            try {
                // 查找订单
                let order = this.orderService.assignedOrders.find(o => o.id === orderId);
                let isAssigned = true;

                if (!order) {
                    order = this.orderService.orders.find(o => o.id === orderId);
                    isAssigned = false;
                }

                if (!order) {
                    console.warn(`[OrderStore] 未找到订单 ${orderId}，无法更新`);
                    return false;
                }

                // 获取订单类型
                const orderType = order.type || 'PICKUP';

                // 从API获取最新订单数据
                const updatedOrderData = await this.orderService.api.getOrder(orderId, orderType);

                if (!updatedOrderData) {
                    console.warn(`[OrderStore] 无法从API获取订单 ${orderId} 的最新数据`);
                    return false;
                }

                // 更新订单数据
                Object.assign(order, updatedOrderData);

                // 检查订单是否需要移动到另一个列表
                if (isAssigned && !order.route_id) {
                    // 从已分配移到未分配
                    const index = this.orderService.assignedOrders.findIndex(o => o.id === orderId);
                    if (index !== -1) {
                        const movedOrder = this.orderService.assignedOrders.splice(index, 1)[0];
                        this.orderService.orders.push(movedOrder);
                    }
                } else if (!isAssigned && order.route_id) {
                    // 从未分配移到已分配
                    const index = this.orderService.orders.findIndex(o => o.id === orderId);
                    if (index !== -1) {
                        const movedOrder = this.orderService.orders.splice(index, 1)[0];
                        this.orderService.assignedOrders.push(movedOrder);
                    }
                }

                // 使缓存失效
                this.invalidateCache();

                return true;
            } catch (error) {
                console.error(`[OrderStore] 更新订单 ${orderId} 失败:`, error);
                return false;
            }
        },

        // 在API请求前直接更新本地订单状态 - 分配操作
        updateLocalOrdersAfterAssign(orderIds, routeId) {
            console.log(`[OrderStore] 立即更新本地状态: 分配 ${orderIds.length} 个订单到路线 ${routeId}`);

            // 首先尝试获取路线对应的司机ID
            this.getDriverIdForRoute(routeId).then(driverId => {
                if (driverId) {
                    console.log(`[OrderStore] 获取到路线 ${routeId} 对应的司机ID: ${driverId}`);

                    // 遍历每个要分配的订单
                    orderIds.forEach(orderId => {
                        // 查找订单
                        const order = this.orders.find(o => o.id === orderId);
                        if (order) {
                            // 保存原始状态，便于在API失败时回滚
                            order._previousState = {
                                route_id: order.route_id,
                                driver_id: order.driver_id,
                                stop_no: order.stop_no,
                                status: order.status
                            };

                            // 更新订单属性
                            order.route_id = routeId;
                            order.route_number = routeId; // 保持兼容性
                            order.driver_id = driverId; // 设置司机ID
                            order.status = 'assigned';

                            // 从未分配列表移到已分配列表
                            const index = this.orders.findIndex(o => o.id === orderId);
                            if (index !== -1) {
                                const movedOrder = this.orders.splice(index, 1)[0];
                                this.assignedOrders.push(movedOrder);
                                console.log(`[OrderStore] 订单 ${orderId} 已从未分配列表移到已分配列表`);
                            }
                        } else {
                            // 也检查已分配订单列表，可能只是变更了路线
                            const assignedOrder = this.assignedOrders.find(o => o.id === orderId);
                            if (assignedOrder) {
                                // 保存原始状态
                                assignedOrder._previousState = {
                                    route_id: assignedOrder.route_id,
                                    driver_id: assignedOrder.driver_id,
                                    stop_no: assignedOrder.stop_no,
                                    status: assignedOrder.status
                                };

                                // 更新路线ID和司机ID
                                assignedOrder.route_id = routeId;
                                assignedOrder.route_number = routeId;
                                assignedOrder.driver_id = driverId; // 设置司机ID
                                console.log(`[OrderStore] 已分配订单 ${orderId} 更新路线为 ${routeId}, 司机为 ${driverId}`);
                            }
                        }
                    });
                } else {
                    console.warn(`[OrderStore] 未能获取到路线 ${routeId} 对应的司机ID，尝试使用常规方法更新`);
                    // 备用方法：不依赖司机ID的更新
                    this.updateOrdersWithoutDriverId(orderIds, routeId);
                }

                // 使缓存失效
                this.invalidateCache();

                // 立即触发UI更新
                this.refreshOrderDisplay();
            }).catch(error => {
                console.error(`[OrderStore] 获取路线 ${routeId} 司机ID失败:`, error);
                // 出错时使用备用方法
                this.updateOrdersWithoutDriverId(orderIds, routeId);

                // 使缓存失效
                this.invalidateCache();

                // 立即触发UI更新
                this.refreshOrderDisplay();
            });
        },

        // 备用方法：不依赖司机ID的订单更新
        updateOrdersWithoutDriverId(orderIds, routeId) {
            // 遍历每个要分配的订单
            orderIds.forEach(orderId => {
                // 查找订单
                const order = this.orders.find(o => o.id === orderId);
                if (order) {
                    // 保存原始状态，便于在API失败时回滚
                    order._previousState = {
                        route_id: order.route_id,
                        driver_id: order.driver_id,
                        stop_no: order.stop_no,
                        status: order.status
                    };

                    // 更新订单属性，仅设置路线ID
                    order.route_id = routeId;
                    order.route_number = routeId; // 保持兼容性
                    order.status = 'assigned';

                    // 从未分配列表移到已分配列表
                    const index = this.orders.findIndex(o => o.id === orderId);
                    if (index !== -1) {
                        const movedOrder = this.orders.splice(index, 1)[0];
                        this.assignedOrders.push(movedOrder);
                        console.log(`[OrderStore] 订单 ${orderId} 已从未分配列表移到已分配列表`);
                    }
                } else {
                    // 也检查已分配订单列表，可能只是变更了路线
                    const assignedOrder = this.assignedOrders.find(o => o.id === orderId);
                    if (assignedOrder) {
                        // 保存原始状态
                        assignedOrder._previousState = {
                            route_id: assignedOrder.route_id,
                            driver_id: assignedOrder.driver_id,
                            stop_no: assignedOrder.stop_no,
                            status: assignedOrder.status
                        };

                        // 更新路线ID
                        assignedOrder.route_id = routeId;
                        assignedOrder.route_number = routeId;
                        console.log(`[OrderStore] 已分配订单 ${orderId} 更新路线为 ${routeId}`);
                    }
                }
            });
        },

        // 获取路线对应的司机ID
        async getDriverIdForRoute(routeId) {
            try {
                // 先从路线仓库获取路线信息
                const route = await this.orderService.routeRepository.getRouteById(routeId);
                if (route && route.driver_id) {
                    return route.driver_id;
                }

                // 如果路线仓库没有，从路线API获取
                // 首先构建请求参数
                const timeStore = useTimeStore();
                const params = timeStore ? {
                    date: timeStore.selectedDate,
                    priority: timeStore.selectedShift?.value
                } : {};

                // 获取所有路线
                const routes = await this.orderService.routeRepository.getRoutes(params);
                const targetRoute = routes.find(r => r.id === routeId);

                if (targetRoute) {
                    return targetRoute.driver || targetRoute.driver_id;
                }

                return null;
            } catch (error) {
                console.error(`[OrderStore] 获取路线 ${routeId} 的司机ID失败:`, error);
                return null;
            }
        },

        // 在API请求前直接更新本地订单状态 - 取消分配操作
        updateLocalOrdersAfterUnassign(orderIds) {
            console.log(`[OrderStore] 立即更新本地状态: 取消分配 ${orderIds.length} 个订单`);

            // 存储受影响的路线ID，用于后续通知
            const affectedRouteIds = [];

            // 遍历每个要取消分配的订单
            orderIds.forEach(orderId => {
                // 主要在已分配订单中查找
                const order = this.assignedOrders.find(o => o.id === orderId);
                if (order) {
                    // 保存原始状态，便于在API失败时回滚
                    order._previousState = {
                        route_id: order.route_id,
                        driver_id: order.driver_id,
                        stop_no: order.stop_no,
                        status: order.status
                    };

                    // 记录路线ID，用于后续通知
                    if (order.route_id && !affectedRouteIds.includes(order.route_id)) {
                        affectedRouteIds.push(order.route_id);
                    }

                    // 保存原始值以便追踪更改
                    order._previousRouteId = order.route_id;
                    order._previousDriverId = order.driver_id;
                    order._previousStopNo = order.stop_no;

                    // 更新订单状态
                    order.route_id = null;
                    order.route_number = null;
                    order.driver_id = null;
                    order.stop_no = null;
                    order.status = 'pending';

                    // 从已分配列表移到未分配列表
                    const index = this.assignedOrders.findIndex(o => o.id === orderId);
                    if (index !== -1) {
                        const movedOrder = this.assignedOrders.splice(index, 1)[0];
                        this.orders.push(movedOrder);
                        console.log(`[OrderStore] 订单 ${orderId} 已从已分配列表移到未分配列表`);
                    }
                }
            });

            // 重新分配受影响路线中剩余订单的停靠点编号
            affectedRouteIds.forEach(routeId => {
                console.log(`[OrderStore] 重新分配路线 ${routeId} 中剩余订单的停靠点编号`);

                // 获取该路线的所有剩余订单
                const remainingOrders = this.assignedOrders.filter(o => o.route_id === routeId);

                if (remainingOrders.length > 0) {
                    // 按照当前的停靠点编号排序
                    remainingOrders.sort((a, b) => (a.stop_no || 0) - (b.stop_no || 0));

                    // 重新分配停靠点编号
                    remainingOrders.forEach((order, index) => {
                        const newStopNo = index + 1;
                        if (order.stop_no !== newStopNo) {
                            console.log(`[OrderStore] 更新订单 ${order.id} 的停靠点编号: ${order.stop_no} → ${newStopNo}`);
                            order.stop_no = newStopNo;
                        }
                    });

                    console.log(`[OrderStore] 路线 ${routeId} 重新分配停靠点编号完成，剩余 ${remainingOrders.length} 个订单`);
                } else {
                    console.log(`[OrderStore] 路线 ${routeId} 没有剩余订单`);
                }
            });

            // 使缓存失效
            this.invalidateCache();

            // 立即触发UI更新
            this.refreshOrderDisplay();

            // 直接重新绘制受影响的路线（与 DriverRoutePanel 相同的方式）
            if (affectedRouteIds.length > 0 && window.mapRouteManagement && window.mapRouteManagement.drawRealisticRoute) {
                console.log(`[OrderStore] 开始重新绘制受影响的路线: ${affectedRouteIds.join(', ')}`);

                // 延迟执行，确保UI更新完成
                setTimeout(() => {
                    affectedRouteIds.forEach(async (routeId) => {
                        console.log(`[OrderStore] 重新绘制路线: ${routeId}`);

                        // 获取路线信息
                        const routeStore = window.routeStore;
                        const targetRoute = routeStore?.getRouteById ? routeStore.getRouteById(routeId) : null;

                        if (!targetRoute) {
                            console.warn(`[OrderStore] 找不到路线 ${routeId}`);
                            return;
                        }

                        const routeDriverId = targetRoute.driver || targetRoute.driverId;

                        if (!routeDriverId) {
                            console.warn(`[OrderStore] 路线 ${routeId} 没有司机ID`);
                            return;
                        }

                        // 获取更新后的订单列表（应该已经移除了取消分配的订单并重新编号）
                        const updatedRouteOrders = this.getOrdersByRouteNumber(routeId) || [];
                        console.log(`[OrderStore] 路线 ${routeId} 取消分配后的订单数量: ${updatedRouteOrders.length}`);

                        if (updatedRouteOrders.length > 0) {
                            try {
                                console.log(`🚀 [OrderStore] 取消分配完成: 触发重新绘制真实路线, routeId: ${routeId}`);
                                console.log(`📋 [OrderStore] 取消分配使用订单顺序:`, updatedRouteOrders.map(o => ({ id: o.id, stop_no: o.stop_no, name: o.name })));

                                // 调用真实路线绘制函数（与 DriverRoutePanel 完全相同的调用方式）
                                await window.mapRouteManagement.drawRealisticRoute(routeId, routeDriverId, updatedRouteOrders);
                                console.log(`✅ [OrderStore] 取消分配后真实路线重绘完成`);
                            } catch (error) {
                                console.error(`❌ [OrderStore] 取消分配后重新绘制真实路线失败:`, error);
                            }
                        } else {
                            console.log(`[OrderStore] 路线 ${routeId} 没有剩余订单，隐藏路线`);
                            // 如果路线没有剩余订单，隐藏该路线
                            if (window.mapRouteManagement && window.mapRouteManagement.hideRoute) {
                                window.mapRouteManagement.hideRoute(routeId);
                            }
                        }
                    });
                }, 500); // 延迟500ms，确保所有更新完成
            } else {
                console.warn(`[OrderStore] 无法重新绘制路线：mapRouteManagement 不可用`);
            }

            // 返回受影响的路线ID，方便后续使用
            return affectedRouteIds;
        },

        // 直接使用司机ID更新本地订单状态
        updateLocalOrdersWithDriverId(orderIds, routeId, driverId) {
            console.log(`[OrderStore] 立即更新本地状态: 分配 ${orderIds.length} 个订单到路线 ${routeId}, 司机 ${driverId}`);

            // 遍历每个要分配的订单
            orderIds.forEach(orderId => {
                // 查找订单
                const order = this.orders.find(o => o.id === orderId);
                if (order) {
                    // 保存原始状态，便于在API失败时回滚
                    order._previousState = {
                        route_id: order.route_id,
                        driver_id: order.driver_id,
                        stop_no: order.stop_no,
                        status: order.status
                    };

                    // 更新订单属性
                    order.route_id = routeId;
                    order.route_number = routeId; // 保持兼容性
                    order.driver_id = driverId; // 设置司机ID
                    order.status = 'assigned';

                    // 从未分配列表移到已分配列表
                    const index = this.orders.findIndex(o => o.id === orderId);
                    if (index !== -1) {
                        const movedOrder = this.orders.splice(index, 1)[0];
                        this.assignedOrders.push(movedOrder);
                        console.log(`[OrderStore] 订单 ${orderId} 已从未分配列表移到已分配列表`);
                    }
                } else {
                    // 也检查已分配订单列表，可能只是变更了路线
                    const assignedOrder = this.assignedOrders.find(o => o.id === orderId);
                    if (assignedOrder) {
                        // 保存原始状态
                        assignedOrder._previousState = {
                            route_id: assignedOrder.route_id,
                            driver_id: assignedOrder.driver_id,
                            stop_no: assignedOrder.stop_no,
                            status: assignedOrder.status
                        };

                        // 更新路线ID和司机ID
                        assignedOrder.route_id = routeId;
                        assignedOrder.route_number = routeId;
                        assignedOrder.driver_id = driverId; // 设置司机ID
                        console.log(`[OrderStore] 已分配订单 ${orderId} 更新路线为 ${routeId}, 司机为 ${driverId}`);
                    }
                }
            });

            // 使缓存失效
            this.invalidateCache();

            // 立即触发UI更新
            this.refreshOrderDisplay();
        },

        // 从API刷新订单信息（用于替代assignOrdersToRouteWithAPI，特别是在创建新路线后）
        async refreshOrdersFromAPI(orders, routeId, driverId = null) {
            try {
                // 提取订单ID
                const orderIds = orders.map(o => o.id);
                console.log(`[OrderStore] 从API刷新 ${orderIds.length} 个订单信息，路线 ${routeId}, 司机 ${driverId || '未指定'}`);

                // 1. 首先进行本地乐观更新
                if (driverId) {
                    this.updateLocalOrdersWithDriverId(orderIds, routeId, driverId);
                } else {
                    this.updateLocalOrdersAfterAssign(orderIds, routeId);
                }

                // 2. 然后从API获取最新订单信息并更新本地状态
                const result = await this.orderService.refreshOrdersFromAPI(orderIds, routeId, driverId);

                // 3. 刷新UI显示
                this.refreshOrderDisplay();

                return result;
            } catch (error) {
                console.error('[OrderStore] 从API刷新订单信息失败:', error);
                // 发生错误时可能需要重新获取数据
                this.invalidateCache();
                throw error;
            }
        }
    }
})