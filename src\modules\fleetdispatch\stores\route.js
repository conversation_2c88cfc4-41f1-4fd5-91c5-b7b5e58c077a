import { defineStore } from 'pinia'
import { useOrderStore } from './order'
import { useTimeStore } from './time'
import { RouteService } from '../services/RouteService'
import { ref } from 'vue'

export const useRouteStore = defineStore('fleetdispatch-route', {
    state: () => ({
    // 创建RouteService实例
        routeService: new RouteService(),
        // 添加选中路线状态
        selectedRoute: null
    }),

    getters: {
    // 获取所有路线
        routes: state => {
            return state.routeService.routes
        },

        // 获取当前路线编号
        currentRouteNumber: state => {
            return state.routeService.currentRouteNumber
        }
    },

    actions: {
    // 设置选中路线
        setSelectedRoute(route) {
            this.selectedRoute = route
        },

        // 清除选中路线
        clearSelectedRoute() {
            this.selectedRoute = null
        },

        // 获取路线数据
        fetchRoutes(params) {
            try {
                return this.routeService.fetchRoutes(params)
            } catch (error) {
                console.error('获取路线失败:', error)
                return []
            }
        },

        // 创建新路线
        createRoute(driverId, orders) {
            return this.routeService.createRoute(driverId, orders)
        },

        // 使用API创建路线
        async createRouteWithAPI(routeData) {
            return this.routeService.createRouteWithAPI(routeData)
        },

        // 分配路线给司机
        async assignRouteToDriver(routeId, driverId) {
            return this.routeService.assignRouteToDriver(routeId, driverId)
        },

        // 更新路线状态
        async updateRouteStatus(routeId, status) {
            try {
                return await this.routeService.updateRouteStatus(routeId, status)
            } catch (error) {
                console.error('更新路线状态失败:', error)
                throw error
            }
        },

        // 更新路线订单
        updateRouteOrders(routeNumber, orders) {
            return this.routeService.updateRouteOrders(routeNumber, orders)
        },

        // 获取司机的活动路线
        getDriverActiveRoute(driverId) {
            return this.routeService.getDriverActiveRoute(driverId)
        },

        // 获取所有活动路线
        getActiveRoutes() {
            return this.routeService.getActiveRoutes()
        },

        // 添加订单到路线
        async addOrderToRoute(order, routeNumber) {
            return this.routeService.addOrderToRoute(order, routeNumber)
        },

        // 从路线中移除订单
        async removeOrderFromRoute(orderId, routeNumber) {
            return this.routeService.removeOrderFromRoute(orderId, routeNumber)
        },

        // 获取当前所有路线的 bucket_sn 集合
        getCurrentBuckets() {
            return this.routeService.getCurrentBuckets()
        },

        // 获取下一个可用的 bucket_sn
        getNextAvailableBucket() {
            return this.routeService.getNextAvailableBucket()
        },

        // 根据ID获取路线
        getRouteById(routeId) {
            return this.routeService.routes.find(route => route.id === routeId || route.routeNumber === routeId)
        },

        // 清除所有路线数据和缓存
        clearAllData() {
            console.log('[RouteStore] 清除所有路线数据和缓存...')

            // 清除 RouteService 中的数据
            this.routeService.routes.length = 0
            this.routeService.currentRouteNumber = 1

            // 清除选中状态
            this.selectedRoute = null

            console.log('[RouteStore] 路线数据清理完成')
        }
    }
})
