// src/modules/fleetdispatch/components/MapView/composables/useMapInitialization.js
import { ref } from 'vue';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import { MAPTILER_API_KEY } from '../../../config/keys'; // 确保路径正确
import { useMapStore } from '../../../stores/map'; // 确保路径正确

export function useMapInitialization(mapContainerRef) {
    const map = ref(null);
    const isMapLoaded = ref(false);
    const mapStore = useMapStore();

    // 初始化地图
    const initializeMap = (onLoadCallback) => {
        console.log('开始初始化地图')

        map.value = new maplibregl.Map({
            container: mapContainerRef.value,
            style: `https://api.maptiler.com/maps/streets/style.json?key=${MAPTILER_API_KEY}`,
            center: [-79.346574, 43.818219], // 多伦多北约克的坐标
            zoom: 11,
            attributionControl: false,
            maxZoom: 22,
            minZoom: 3,
            dragRotate: false,
            pitchWithRotate: false,
            touchZoomRotate: true,
            trackResize: true,
            boxZoom: false, // 禁用默认的框选功能
            logoPosition: 'bottom-right',
            failIfMajorPerformanceCaveat: true,
            preserveDrawingBuffer: false,
            refreshExpiredTiles: false,
            fadeDuration: 0
        })

        // 监听样式加载完成事件
        map.value.on('style.load', () => {
            console.log('地图样式加载完成')

            // 检查并确保图层可见性
            if (map.value.getLayer('unassigned-orders-layer') &&
                map.value.getLayer('assigned-orders-layer') &&
                map.value.getLayer('selected-orders-layer')) {

                map.value.setLayoutProperty('unassigned-orders-layer', 'visibility', 'visible')
                map.value.setLayoutProperty('assigned-orders-layer', 'visibility', 'visible')
                map.value.setLayoutProperty('selected-orders-layer', 'visibility', 'visible')

                // 确保标记图层在最上层
                moveSymbolLayersToTop()
            }
        })

        map.value.on('load', () => {
            console.log('地图加载完成')
            addMapControls()
            isMapLoaded.value = true
            mapStore.setMapReady(true)

            // 添加标记图层应该在地图加载完成后，即使没有提供回调
            moveSymbolLayersToTop()

            // 创建起点和终点图标
            createRouteEndpointIcons()

            // 调用 MapView.vue 传入的回调函数
            if (onLoadCallback && typeof onLoadCallback === 'function') {
                onLoadCallback()
            }

            // 添加地图渲染完成后的标记可见性检查
            setTimeout(() => {
                if (map.value) {
                    // 确保所有图层可见
                    if (map.value.getLayer('unassigned-orders-layer')) {
                        map.value.setLayoutProperty('unassigned-orders-layer', 'visibility', 'visible')
                    }
                    if (map.value.getLayer('assigned-orders-layer')) {
                        map.value.setLayoutProperty('assigned-orders-layer', 'visibility', 'visible')
                    }
                    if (map.value.getLayer('selected-orders-layer')) {
                        map.value.setLayoutProperty('selected-orders-layer', 'visibility', 'visible')
                    }
                }
            }, 500)
        })

        // 添加错误处理
        map.value.on('error', (e) => {
            console.error('MapLibre GL JS Error:', e)
        })
    }

    // 添加地图控件
    const addMapControls = () => {
        if (!map.value) return

        // 添加全屏控件
        map.value.addControl(new maplibregl.FullscreenControl(), 'top-right')

        // 添加缩放控件
        map.value.addControl(new maplibregl.NavigationControl(), 'top-right')

        // 添加定位控件
        map.value.addControl(
            new maplibregl.GeolocateControl({
                positionOptions: {
                    enableHighAccuracy: true
                },
                trackUserLocation: true
            }),
            'top-right'
        )
    }

    // 确保标记图层显示在路线图层之上，但不是最顶层
    const moveSymbolLayersToTop = () => {
        if (!map.value) return

        try {
            // 获取所有图层
            const layers = map.value.getStyle().layers
            if (!layers) return

            // 找到所有路线图层
            const routeLayerIds = []
            const symbolLayerIds = [
                'unassigned-orders-layer',
                'assigned-orders-layer',
                'selected-orders-layer'
            ]

            // 收集所有路线图层ID
            layers.forEach(layer => {
                if (layer.id.startsWith('route-layer-')) {
                    routeLayerIds.push(layer.id)
                }
            })

            console.log('找到的路线图层:', routeLayerIds)

            // 如果有路线图层，将标记图层移动到路线图层之上
            if (routeLayerIds.length > 0) {
                // 找到最上层的路线图层
                const topRouteLayerId = routeLayerIds[routeLayerIds.length - 1]

                // 将司机图层移动到路线图层之上（如果存在）
                if (map.value.getLayer('drivers-layer')) {
                    map.value.moveLayer('drivers-layer', topRouteLayerId)
                    console.log('司机图层已移动到路线图层之上')
                }

                // 将订单标记图层移动到路线图层之上
                symbolLayerIds.forEach(layerId => {
                    if (map.value.getLayer(layerId)) {
                        // 如果司机图层存在，将订单图层移动到司机图层之上
                        // 否则移动到最上层的路线图层之上
                        const beforeLayerId = map.value.getLayer('drivers-layer') ? 'drivers-layer' : topRouteLayerId
                        map.value.moveLayer(layerId, beforeLayerId)
                        console.log(`订单图层 ${layerId} 已移动到路线图层之上`)
                    }
                })
            } else {
                // 如果没有路线图层，使用原有逻辑
                // 找出所有需要移动到顶部的图层ID
                const allSymbolLayerIds = []
                for (const layer of layers) {
                    if (layer.type === 'symbol' ||
                        layer.id === 'unassigned-orders-layer' ||
                        layer.id === 'assigned-orders-layer' ||
                        layer.id === 'selected-orders-layer') {
                        allSymbolLayerIds.push(layer.id)
                    }
                }

                // 将这些图层移到最上层
                for (const layerId of allSymbolLayerIds) {
                    map.value.moveLayer(layerId)
                }
                console.log('没有路线图层，使用默认图层顺序')
            }

            console.log('已将标记图层移至路线图层之上')
        } catch (error) {
            console.error('移动图层时出错:', error)
        }
    }

    // 创建路线起点和终点图标
    const createRouteEndpointIcons = () => {
        if (!map.value) return;

        console.log('开始创建路线起点和终点图标');

        // 创建起点图标（绿色旗帜）
        const startIcon = new Image(24, 24);
        startIcon.onload = () => {
            if (map.value && !map.value.hasImage('start-point-icon')) {
                map.value.addImage('start-point-icon', startIcon);
                console.log('已创建起点图标');
            }
        };
        startIcon.src = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(`
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#4CAF50" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path>
                <line x1="4" y1="22" x2="4" y2="15"></line>
            </svg>
        `);

        // 创建终点图标（红色标记）
        const endIcon = new Image(24, 24);
        endIcon.onload = () => {
            if (map.value && !map.value.hasImage('end-point-icon')) {
                map.value.addImage('end-point-icon', endIcon);
                console.log('已创建终点图标');
            }
        };
        endIcon.src = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(`
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#F44336" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
            </svg>
        `);
    }

    // 销毁地图
    const removeMap = () => {
        if (map.value) {
            console.log('移除地图实例')
            map.value.remove()
            map.value = null
            isMapLoaded.value = false
            mapStore.setMapReady(false)
        }
    }

    return {
        map,
        isMapLoaded,
        initializeMap,
        removeMap,
        moveSymbolLayersToTop,
        createRouteEndpointIcons
    }
}