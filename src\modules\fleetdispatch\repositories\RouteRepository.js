import { Route } from '../models/Route'
import { routeAPI } from '../api'

export class RouteRepository {
    // 获取路线列表
    async getRoutes(params) {
        try {
            const response = await routeAPI.getRoutes(params)
            console.log('API返回的路线数据:', response)

            if (!response || !Array.isArray(response)) {
                console.warn('API返回的路线数据格式不正确:', response)
                return []
            }

            // 将API返回的普通对象转换为Route对象
            const routes = response.map(route => {
                // 处理坐标信息
                if (route.start_location) {
                    route.location = route.start_location
                }

                // 记录调试信息
                console.log('处理路线数据:', {
                    routeId: route.id || route.routeNumber,
                    driverId: route.driver,
                    orders: route.orders?.length || 0,
                    start_point: route.start_point,
                    end_point: route.end_point
                })

                const routeObject = new Route(route)

                // 验证起点和终点是否正确设置
                console.log('创建的Route对象:', {
                    id: routeObject.id,
                    start_point: routeObject.start_point,
                    end_point: routeObject.end_point
                })

                return routeObject
            })

            return routes
        } catch (error) {
            console.error('获取路线失败:', error)
            throw error
        }
    }

    // 使用API创建新路线
    async createRouteWithAPI(routeData) {
        try {
            const response = await routeAPI.createRoute(routeData)
            console.log('API创建路线返回数据:', response)

            // --- 修改后的检查逻辑 ---
            // 首先检查是否是包含 errCode: 365 且有 id 的成功响应
            if (response && response.data && response.data.errCode === 365 && response.data.id) {
                console.log('路线创建成功 (errCode 365):', response.data)
                const createdData = response.data;
                const route = new Route({
                    id: createdData.id, // 使用返回的ID
                    routeNumber: createdData.id, // 保持兼容或根据需要调整
                    driverId: routeData.driver, // 从请求数据中获取 driverId
                    driver: routeData.driver, // 添加 driver 属性
                    status: 'active', // 假设新创建即为 active
                    name: routeData.name, // 从请求数据获取 name
                    priority: routeData.priority, // 从请求数据获取 priority
                    date: routeData.date, // 从请求数据获取 date
                    bucket_sn: routeData.bucket_sn, // 从请求数据获取 bucket_sn
                    start_point: routeData.start_point, // 添加起点
                    end_point: routeData.end_point, // 添加终点
                    orders: [] // 初始订单为空
                })
                return route;
            }

            // 其次检查是否是包含 msg: '...created...' 且有 id 的成功响应
            // (注意：这可能与上面的情况重叠，但作为备用检查)
            if (response && response.data && response.data.id &&
                (response.data.msg && response.data.msg.toLowerCase().includes('created'))) {
                console.log('路线创建成功 (msg includes created):', response.data)
                 const createdData = response.data;
                 const route = new Route({
                    id: createdData.id,
                    routeNumber: createdData.id,
                    driverId: routeData.driver,
                    driver: routeData.driver,
                    status: 'active',
                    name: routeData.name,
                    priority: routeData.priority,
                    date: routeData.date,
                    bucket_sn: routeData.bucket_sn, // 从请求数据获取 bucket_sn
                    start_point: routeData.start_point, // 添加起点
                    end_point: routeData.end_point, // 添加终点
                    orders: []
                })
                return route;
            }

            // 检查是否存在普通的成功响应（例如直接返回路线对象，虽然当前API似乎不是这样）
            if (response && response.id) { // 假设直接返回对象有 id
                 console.log('路线创建成功 (直接返回对象):', response)
                 // 这里需要根据实际返回的对象结构创建 Route
                 const route = new Route({
                     id: response.id,
                     routeNumber: response.id,
                     driverId: response.driver, // 或 response.driver_id
                     driver: response.driver,
                     status: response.status || 'active',
                     name: response.name,
                     priority: response.priority,
                     date: response.date,
                     bucket_sn: response.bucket_sn || routeData.bucket_sn, // 优先使用响应中的，否则使用请求数据
                     start_point: response.start_point || routeData.start_point, // 添加起点
                     end_point: response.end_point || routeData.end_point, // 添加终点
                     orders: response.orders || []
                 })
                 return route;
            }

            // 如果以上条件都不满足，则认为数据异常
            console.warn('API创建路线返回数据异常或格式不符:', response)
            return null

        } catch (error) {
            console.error('API创建路线失败 (catch block):', error)

            // 检查错误响应中是否包含成功信息 (errCode 365)
            if (error.response && error.response.data) {
                const errorData = error.response.data;
                if (errorData.errCode === 365 && errorData.id) {
                    console.log('从错误响应中恢复：路线创建成功 (errCode 365):', errorData);
                    const route = new Route({
                        id: errorData.id,
                        routeNumber: errorData.id,
                        driverId: routeData.driver,
                        driver: routeData.driver,
                        status: 'active',
                        name: routeData.name,
                        priority: routeData.priority,
                        date: routeData.date,
                        bucket_sn: routeData.bucket_sn, // 从请求数据获取 bucket_sn
                        start_point: routeData.start_point, // 添加起点
                        end_point: routeData.end_point, // 添加终点
                        orders: []
                    });
                    return route;
                }
                // 可以添加检查 msg contains 'created' 的逻辑
                if (errorData.id && (errorData.msg && errorData.msg.toLowerCase().includes('created'))) {
                     console.log('从错误响应中恢复：路线创建成功 (msg includes created):', errorData);
                     const route = new Route({
                         id: errorData.id,
                         routeNumber: errorData.id,
                         driverId: routeData.driver,
                         driver: routeData.driver,
                         status: 'active',
                         name: routeData.name,
                         priority: routeData.priority,
                         date: routeData.date,
                         bucket_sn: routeData.bucket_sn, // 从请求数据获取 bucket_sn
                         start_point: routeData.start_point, // 添加起点
                         end_point: routeData.end_point, // 添加终点
                         orders: []
                     });
                     return route;
                }
            }

            throw error // 如果无法从错误中恢复，则抛出原始错误
        }
    }

    // 分配路线给司机
    async assignRouteToDriver(routeId, driverId) {
        try {
            const response = await routeAPI.assignRoute(routeId, driverId)
            console.log('API分配路线返回数据:', response)

            if (!response) {
                console.warn('API分配路线返回数据异常')
                return false
            }

            return true
        } catch (error) {
            console.error(`将路线 ${routeId} 分配给司机 ${driverId} 失败:`, error)
            throw error
        }
    }

    // 创建新路线
    createRoute(driverId, orders = []) {
    // 创建新路线对象
    // 注意：这里通常会发送请求到服务器创建路线，然后返回创建的路线
    // 目前我们只在本地创建一个新对象
        const routeNumber = Date.now() // 使用时间戳作为临时ID

        const route = new Route({
            routeNumber,
            driverId,
            status: 'active',
            orders: orders.map((order, index) => ({
                orderId: order.id,
                stopNumber: index + 1
            }))
        })

        return route
    }

    // 更新路线状态
    async updateRouteStatus(routeId, status) {
        try {
            await routeAPI.updateRouteStatus(routeId, status)
            // 假设API调用成功，返回更新后的状态
            return status
        } catch (error) {
            console.error(`更新路线 ${routeId} 状态失败:`, error)
            throw error
        }
    }

    // 获取司机的活动路线
    async getDriverActiveRoute(driverId, routes) {
    // 如果提供了路线列表，从中查找司机的活动路线
        if (routes && Array.isArray(routes)) {
            const route = routes.find(r => r.driverId === driverId && r.status === 'active')
            return route || null
        }

        // 否则，从服务器获取路线
        try {
            const allRoutes = await this.getRoutes({})
            return allRoutes.find(r => r.driverId === driverId && r.status === 'active') || null
        } catch (error) {
            console.error(`获取司机 ${driverId} 的活动路线失败:`, error)
            throw error
        }
    }

    // 通过ID获取路线信息
    async getRouteById(routeId) {
        try {
            // 首先尝试直接获取单个路线的API（如果有）
            try {
                const response = await routeAPI.getRouteById(routeId);
                if (response && (response.id || response.routeNumber)) {
                    return new Route(response);
                }
            } catch (directApiError) {
                console.log(`直接获取路线 ${routeId} 的API不可用或返回错误:`, directApiError);
                // 继续尝试从所有路线中查找
            }

            // 从所有路线中查找
            const allRoutes = await this.getRoutes({});
            const route = allRoutes.find(r => r.id === routeId || r.routeNumber === routeId);
            return route || null;
        } catch (error) {
            console.error(`获取路线 ${routeId} 信息失败:`, error);
            return null;
        }
    }

    // 更新路线的订单
    updateRouteOrders(route, orders) {
        if (!route) return null

        // 更新路线中的订单
        return route.addOrders(orders)
    }
}